[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = *********************:6757983c35730943ed491206/fulfillmen/fulfillmen-shop.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
	vscode-merge-base = origin/develop
	gk-merge-base = origin/develop
[branch "feature/maven-structural-change"]
	vscode-merge-base = origin/develop
[branch "feature/i18n"]
	vscode-merge-base = origin/develop
	gk-merge-base = origin/develop
[branch "feature/rate-limit-openapi"]
	vscode-merge-base = origin/develop
	gk-merge-base = origin/develop
	remote = origin
	merge = refs/heads/feature/rate-limit-openapi
[branch "feature/update-pdc-sync"]
	vscode-merge-base = origin/master
	gk-merge-base = origin/master
	remote = origin
	merge = refs/heads/feature/update-pdc-sync
[branch "feature/chain-sync-product"]
	vscode-merge-base = origin/master
	remote = origin
	merge = refs/heads/feature/chain-sync-product
