# 采购订单列表美元金额计算功能

## 功能概述

已成功为采购订单列表查询功能增加了美元金额的计算转换，根据订单创建时保存的 `exchangeRate` 汇率字段进行精确计算。

## 核心特性

### 1. 订单级别美元金额计算
- **字段**: `totalAmountUsd` - 订单总金额（美元）
- **计算方式**: `totalAmount * exchangeRate`
- **精度**: 保留两位小数，直接截断（不四舍五入）

### 2. 订单项级别美元价格计算
- **字段**: `usdPrice` - 商品单价（美元）
- **计算方式**: `price * exchangeRate`
- **精度**: 保留两位小数，直接截断（不四舍五入）

### 3. 汇率处理策略
- **优先使用**: 订单创建时保存的历史汇率 (`exchangeRate`)
- **兜底机制**: 如果历史汇率无效，使用当前实时汇率
- **数据一致性**: 确保显示的是订单创建时的汇率，而非当前汇率

## 技术实现

### 转换器增强
```java
// 订单总金额美元计算
@Mapping(target = "totalAmountUsd", expression = "java(calculateTotalAmountUsd(tzOrderPurchase.getTotalAmount(), tzOrderPurchase.getExchangeRate()))")

// 订单项美元价格计算
@Mapping(target = "usdPrice", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), exchangeRate))")
```

### 计算方法
```java
/**
 * 根据订单创建时的汇率计算美元金额
 */
default BigDecimal calculateTotalAmountUsd(BigDecimal totalAmount, BigDecimal exchangeRate) {
    if (totalAmount == null) {
        return BigDecimal.ZERO;
    }
    
    if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
        // 兜底：使用当前汇率
        return CurrencyConversionUtils.convertCurrency(totalAmount, "CNY", "USD");
    }
    
    // 使用历史汇率，保留两位小数并截断
    return totalAmount.multiply(exchangeRate).setScale(2, RoundingMode.DOWN);
}
```

## 数据示例

### 订单列表响应示例
```json
{
  "records": [
    {
      "id": 1,
      "orderNo": "PO202507070001",
      "status": "PAYMENT_PENDING",
      "totalAmount": 1000.00,
      "totalAmountUsd": 140.00,
      "itemCount": 2,
      "createTime": "2025-07-07 16:30:00",
      "items": [
        {
          "id": "1",
          "productTitle": "测试商品1",
          "productTitleEn": "Test Product 1",
          "quantity": 2,
          "price": 500.00,
          "usdPrice": 70.00
        },
        {
          "id": "2", 
          "productTitle": "测试商品2",
          "productTitleEn": "Test Product 2",
          "quantity": 1,
          "price": 500.00,
          "usdPrice": 70.00
        }
      ],
      "canCancel": true,
      "canConfirmReceipt": false,
      "canApplyRefund": false
    }
  ]
}
```

## 汇率计算逻辑

### 场景1: 有历史汇率
```
订单创建时汇率: 1 CNY = 0.14 USD
订单总金额: 1000.00 CNY
计算结果: 1000.00 * 0.14 = 140.00 USD
```

### 场景2: 无历史汇率（兜底）
```
历史汇率: null 或 <= 0
订单总金额: 1000.00 CNY
计算方式: 使用 CurrencyConversionUtils.convertCurrency(1000.00, "CNY", "USD")
```

## 业务价值

1. **历史准确性**: 显示订单创建时的真实汇率，避免汇率波动造成的金额差异
2. **用户体验**: 提供双币种显示，方便国际用户理解订单价值
3. **数据一致性**: 确保订单金额在整个生命周期内保持一致
4. **审计追溯**: 保留历史汇率信息，便于财务审计和对账

## 测试验证

已添加完整的单元测试覆盖：
- ✅ 订单总金额美元计算测试
- ✅ 订单项美元价格计算测试  
- ✅ 汇率兜底机制测试
- ✅ 边界条件测试

## 使用方式

前端调用订单列表API时，响应中会自动包含美元金额字段：

```javascript
// API调用
GET /api/orders?page=1&size=10

// 响应数据中包含
{
  totalAmount: 1000.00,      // 人民币金额
  totalAmountUsd: 140.00,    // 美元金额（基于历史汇率）
  items: [
    {
      price: 500.00,         // 人民币单价
      usdPrice: 70.00        // 美元单价（基于历史汇率）
    }
  ]
}
```

功能已完成并通过测试验证！
