import api from '@/api/config/axios'
import type { ApiResponse } from '@/api/types'
import { handleApiError, normalizeResponse } from '@/api/utils/apiUtils'

// 与后端 AttrJson 匹配的规格属性接口
export interface AttrJson {
    attrId?: string
    attrKey: string
    attrValue: string
    attrKeyTrans?: string
    attrValueTrans?: string
    skuImage?: string
}

// 订单预览请求参数 - 与后端OrderPreviewReq匹配
export interface OrderPreviewRequest {
    productList: {
        skuId: number
        productQuantity: number
    }[],
    isShoppingCart: number,
    shoppingCartIds: string
}

// 订单提交请求参数 - 与后端CreateOrderSubmitReq匹配
export interface OrderSubmitRequest {
    idempotentToken: string  // 必需的幂等令牌
    productList: {
        skuId: number
        productQuantity: number
    }[]
    buyerMessage?: string    // 可选的买家留言
}

// 订单预览响应 - 与后端OrderPreviewVO完全匹配，添加令牌字段
export interface OrderPreviewResponse {
    idempotentToken: string      // 幂等令牌
    tokenExpiryTime: string      // 令牌过期时间 (ISO8601格式)
    createTime: string           // 预览创建时间 (ISO8601格式)
    orderPreviewSummary: {
        totalQuantity: number
        productTypeCount: number
        success: boolean
        hasErrors: boolean
    }
    productItems: {
        spuId: number
        specId: string
        skuId: number
        productTitle: string
        productTitleEn: string
        productImageUrl: string
        skuImage: string
        skuSpecs: AttrJson[]
        unitPrice: number         // 后端BigDecimal -> JSON number
        unitPriceUsd: number      // 后端BigDecimal -> JSON number  
        orderedQuantity: number
        lineTotalAmount: number   // 后端BigDecimal -> JSON number
        lineTotalAmountUsd: number // 后端BigDecimal -> JSON number
        unitOfMeasure: string
        available: boolean
        message: string
    }[]
    priceDetails: {
        merchandiseAmount: number     // 后端BigDecimal -> JSON number
        merchandiseAmountUsd: number  // 后端BigDecimal -> JSON number
        shippingAmount: number        // 后端BigDecimal -> JSON number
        shippingAmountUsd: number     // 后端BigDecimal -> JSON number
        serviceFee: number            // 后端BigDecimal -> JSON number
        serviceFeeUsd: number         // 后端BigDecimal -> JSON number
        discountAmount: number        // 后端BigDecimal -> JSON number
        discountAmountUsd: number     // 后端BigDecimal -> JSON number
        totalAmount: number           // 后端BigDecimal -> JSON number
        totalAmountUsd: number        // 后端BigDecimal -> JSON number
    }
    errors: {
        errorCode: string
        errorMessage: string
        offerId?: number
        specId?: string
        errorType?: string
    }[]
}

// 订单提交响应接口
export interface OrderSubmitResponse {
    success?: boolean                // 操作是否成功
    purchaseOrderNo: string         // 采购订单编号
    purchaseOrderId: number | string // 采购订单ID
    message?: string                // 响应消息

    // 完整的OrderSubmitVO字段（如果后端返回完整对象）
    submitSummary?: {
        totalQuantity: number
        productTypeCount: number
        submitTime: string
    }
    priceDetails?: {
        merchandiseAmount: number
        merchandiseAmountUsd: number
        shippingAmount: number
        shippingAmountUsd: number
        serviceFee: number
        serviceFeeUsd: number
        discountAmount: number
        discountAmountUsd: number
        totalAmount: number
        totalAmountUsd: number
    }
}

export const orderApi = {
    /**
     * 订单预览
     * @param data 订单预览请求参数
     * @returns 订单预览响应数据（包含幂等令牌）
     */
    async previewOrder(data: OrderPreviewRequest): Promise<OrderPreviewResponse> {
        try {
            const response = await api.post<ApiResponse<OrderPreviewResponse>>('/api/orders/preview', data)
            return normalizeResponse<OrderPreviewResponse>(response.data)
        } catch (error) {
            handleApiError(error, 'order preview failed')
        }
    },

    /**
     * 提交订单
     * @param data 订单提交请求参数（包含幂等令牌）
     * @returns 订单提交响应数据
     */
    async submitOrder(data: OrderSubmitRequest): Promise<OrderSubmitResponse> {
        console.log('API请求数据:', data)

        const response = await api.post<ApiResponse<OrderSubmitResponse>>('/api/orders/submit', data)
        console.log('API原始响应:', response.data)

        const result = normalizeResponse<OrderSubmitResponse>(response.data)
        console.log('处理后的结果:', result)

        return result
    },

    /**
     * 根据令牌查询预览信息
     * @param idempotentToken 幂等令牌
     * @returns 订单预览响应数据
     */
    async getPreviewByToken(idempotentToken: string): Promise<OrderPreviewResponse> {
        try {
            const response = await api.get<ApiResponse<OrderPreviewResponse>>(`/api/orders/preview/${idempotentToken}`)
            return normalizeResponse<OrderPreviewResponse>(response.data)
        } catch (error) {
            handleApiError(error, 'get preview by token failed')
        }
    },

    /**
     * 创建订单 (废弃的接口，保持向后兼容)
     * @deprecated 请使用 submitOrder 替代
     */
    async createOrder(data: any): Promise<any> {
        console.warn('createOrder is deprecated, please use submitOrder instead')
        return this.submitOrder(data)
    },
} 