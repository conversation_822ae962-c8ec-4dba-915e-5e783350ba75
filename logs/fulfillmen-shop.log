2025-07-07 00:05:49 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h7m36s303ms).
2025-07-07 00:22:42 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m53s556ms).
2025-07-07 00:38:47 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m4s980ms).
2025-07-07 00:55:03 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m45s641ms).
2025-07-07 01:29:49 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=34m45s877ms).
2025-07-07 01:48:40 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=18m51s369ms).
2025-07-07 02:21:16 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=32m35s652ms).
2025-07-07 02:55:55 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=34m38s917ms).
2025-07-07 03:31:15 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=35m20s425ms).
2025-07-07 03:47:45 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m30s376ms).
2025-07-07 04:23:19 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=35m33s286ms).
2025-07-07 04:39:38 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-06 22:45:00,007 to 2025-07-07 04:39:38,187
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 04:52:40 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=29m21s246ms).
2025-07-07 05:10:01 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m21s176ms).
2025-07-07 05:26:48 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m46s552ms).
2025-07-07 05:45:35 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=18m47s94ms).
2025-07-07 06:02:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m44s805ms).
2025-07-07 06:21:52 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=19m2s832ms).
2025-07-07 06:23:16 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m23s729ms).
2025-07-07 06:39:36 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m20s131ms).
2025-07-07 06:54:02 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=14m25s782ms).
2025-07-07 06:59:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m18s74ms).
2025-07-07 07:11:17 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=10m57s).
2025-07-07 07:11:44 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094157618799108096]:[0] 从缓存中获取租户ID: 10000
2025-07-07 07:11:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094157618799108096]:[0] 租户缓存未命中: 10000
2025-07-07 07:11:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157618799108096]:[0] 缓存未命中，从数据库加载租户数据: 10000
2025-07-07 07:11:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: tenants
2025-07-07 07:11:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: tenants_info
2025-07-07 07:11:46 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094157625438691328]:[0] 从缓存中获取租户ID: 10000
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094157625438691328]:[0] 租户缓存未命中: 10000
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157625438691328]:[0] 缓存未命中，从数据库加载租户数据: 10000
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: tenants
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: tenants_info
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: tenant_plan_relation
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: tenant_domains
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: tenant_plan_relation
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: tenant_domains
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: tenant_plans
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: tenant_plans
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094157625438691328]:[0] 租户缓存设置成功: 10000, 过期时间: 14400秒
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094157625438691328]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157625438691328]:[0] 数据库加载成功并缓存: 租户ID=10000, 租户名称=Fulfillmen
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157625438691328]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/products/715200531406338)
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094157625438691328]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094157625438691328]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094157625438691328]:[0] auth check result: true
2025-07-07 07:11:47 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157625438691328]:[0] [GET] /api/products/715200531406338
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094157625438691328]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094157618799108096]:[0] 租户缓存设置成功: 10000, 过期时间: 14400秒
2025-07-07 07:11:47 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.controller.ProductController - [1094157625438691328]:[0] 获取商品详情: productId=715200531406338
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094157625438691328]:[0] 获取商品详情VO: 传入ID=715200531406338
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094157625438691328]:[0] 获取或同步产品数据: platformProductId=715200531406338
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094157618799108096]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157618799108096]:[0] 数据库加载成功并缓存: 租户ID=10000, 租户名称=Fulfillmen
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157618799108096]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094157618799108096]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094157618799108096]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094157618799108096]:[0] auth check result: true
2025-07-07 07:11:47 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157618799108096]:[0] [GET] /api/home/<USER>
2025-07-07 07:11:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094157618799108096]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=2/100
2025-07-07 07:11:47 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094157618799108096]:[0] 加载所有类目数据
2025-07-07 07:11:47 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094157625438691328]:[0] 从现有SPU获取产品数据: spuId=715201092369942
2025-07-07 07:11:48 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094157625438691328]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 07:11:48 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094157618799108096]:[0] 忽略表的多租户处理: sys_alibaba_category
2025-07-07 07:11:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094157625438691328]:[0] 从数据库获取商品详情, id: 715200531406338
2025-07-07 07:11:49 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157625438691328]:[0] [GET] /api/products/715200531406338 200 1731ms
2025-07-07 07:11:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094157625438691328]:[0] 清除用户上下文和 MDC 信息
2025-07-07 07:11:49 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094157625438691328]:[0] 清理增强租户上下文
2025-07-07 07:11:49 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157625438691328]:[0] 过滤器清理租户上下文完成
2025-07-07 07:11:53 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157618799108096]:[0] [GET] /api/home/<USER>
2025-07-07 07:11:53 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094157618799108096]:[0] 清除用户上下文和 MDC 信息
2025-07-07 07:11:53 ERROR [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157618799108096]:[0] 租户过滤器处理异常: Broken pipe
java.io.IOException: Broken pipe
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:66)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:227)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:158)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:574)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:679)
	at io.undertow.conduits.ChunkedStreamSinkConduit.doWrite(ChunkedStreamSinkConduit.java:166)
	at io.undertow.conduits.ChunkedStreamSinkConduit.write(ChunkedStreamSinkConduit.java:128)
	at io.undertow.conduits.DeflatingStreamSinkConduit.performFlushIfRequiredSingleBuffer(DeflatingStreamSinkConduit.java:445)
	at io.undertow.conduits.DeflatingStreamSinkConduit.performFlushIfRequired(DeflatingStreamSinkConduit.java:432)
	at io.undertow.conduits.DeflatingStreamSinkConduit.deflateData(DeflatingStreamSinkConduit.java:522)
	at io.undertow.conduits.DeflatingStreamSinkConduit.write(DeflatingStreamSinkConduit.java:130)
	at io.undertow.conduits.DeflatingStreamSinkConduit.write(DeflatingStreamSinkConduit.java:165)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2171)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeBlocking(ServletOutputStreamImpl.java:1002)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:228)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.util.FastByteArrayOutputStream.writeTo(FastByteArrayOutputStream.java:271)
	at org.springframework.web.util.ContentCachingResponseWrapper.copyBodyToResponse(ContentCachingResponseWrapper.java:310)
	at org.springframework.web.util.ContentCachingResponseWrapper.copyBodyToResponse(ContentCachingResponseWrapper.java:290)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.updateResponse(LogFilter.java:136)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:73)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:98)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-07 07:11:53 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094157618799108096]:[0] auth check result: true
2025-07-07 07:11:53 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157618799108096]:[0] [GET] /api/home/<USER>
2025-07-07 07:11:53 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094157618799108096]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=3/100
2025-07-07 07:11:53 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094157618799108096]:[0] 加载所有类目数据
2025-07-07 07:11:53 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094157618799108096]:[0] [GET] /api/home/<USER>
2025-07-07 07:11:54 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094157618799108096]:[0] 清除用户上下文和 MDC 信息
2025-07-07 07:11:54 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094157618799108096]:[0] 清理增强租户上下文
2025-07-07 07:11:54 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094157618799108096]:[0] 过滤器清理租户上下文完成
2025-07-07 07:29:54 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=18m6s639ms).
2025-07-07 07:48:24 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m59s655ms).
2025-07-07 08:00:11 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m47s332ms).
2025-07-07 08:34:14 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=34m3s401ms).
2025-07-07 08:55:54 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=21m39s371ms).
2025-07-07 09:21:12 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=25m17s899ms).
2025-07-07 09:21:15 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 04:39:38,187 to 2025-07-07 09:21:12,048
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00| 50.00%|             2|             1|             0|             0|    4,528.0|      4,528
alibaba:category:list:_local |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:25:35 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:25:35 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751851535775}] instanceId:[InstanceId{instanceId=************:48475, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:25:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 09:25:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:25:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 09:25:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 09:25:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 09:25:40 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 09:25:40 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 81990 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-07 09:25:40 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-07 09:25:40 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-07 09:25:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 09:25:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 09:25:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-07 09:25:41 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2297 ms
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-07 09:25:42 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-07 09:25:42 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-07 09:25:42 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-07 09:25:43 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 09:25:43 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 09:25:43 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-07 09:25:43 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 09:25:43 INFO  [redisson-netty-1-4] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-07 09:25:43 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-07 09:25:43 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 09:25:43 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, MANUAL, DISABLED]
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@22367b8
2025-07-07 09:25:44 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:81990, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1751851544872}] - instanceId:[InstanceId{instanceId=**************:81990, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 09:25:44 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 09:25:45 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 60 ms
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-07 09:25:45 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 5.946 seconds (process running for 6.846)
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-07 09:25:45 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-07 09:25:45 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-07 09:25:45 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-07 09:25:45 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-07 09:25:45 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-07 09:25:45 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-07 09:25:46 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-07 09:25:46 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-07 09:25:46 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-07 09:25:46 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-07 09:25:46 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-07 09:25:46 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-07 09:25:46 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-07 09:25:46 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-07 09:25:47 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 09:25:47 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 09:25:47 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 09:25:47 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 09:25:47 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@60f71b9a
2025-07-07 09:25:47 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191470166859776]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191470166859777]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191470166859776]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191470166859776]:[0] 租户缓存命中: 10000
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191470166859777]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191470166859777]:[0] 租户缓存命中: 10000
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859776]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859777]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191470166859776]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859776]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191470166859777]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859777]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191470166859777]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191470166859777]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191470166859776]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191470166859776]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191470166859776]:[0] auth check result: true
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191470166859777]:[0] auth check result: true
2025-07-07 09:26:15 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191470166859776]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:15 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191470166859777]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191470166859777]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191470166859776]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=1/100
2025-07-07 09:26:15 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191470166859777]:[0] 搜索电子产品（带缓存和同步入库）
2025-07-07 09:26:15 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191470166859776]:[0] 加载热门商品列表（带缓存和同步入库）
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 开始获取推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 推荐商品缓存获取开始，语言: EN, 页码: 1, 强制刷新: false
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 推荐商品缓存未命中，执行同步, key: recommend:EN:1:20
2025-07-07 09:26:15 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 开始同步推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 缓存未命中，执行搜索, fullKey: search:65d43bf503e3410204fa882720c56e62, baseKey: search:27eac5f609a7339af532c14c7e27214e:nopag
2025-07-07 09:26:15 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 开始同步搜索商品数据，请求参数: ProductSearchRequestDTO(keyword=null, language=null, page=1, pageSize=10, categoryId=null, categoryIdList=null, regionOpp=null, productCollectionId=262069983, snId=null, keywordTranslate=null, minPrice=null, maxPrice=null, sortField=null, sortOrder=null, filter=null, imageId=null, imageUrl=null, saleFilterParams=null)
2025-07-07 09:26:15 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191470166859776]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.offerRecommend/8390330_aop_timestamp1751851575750access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2recommendOfferParam{"country":"en","beginPage":1,"pageSize":20,"outMemberId":null} 签名: 68D9795022ED9EA814893995D3AB03FA384A6297
2025-07-07 09:26:15 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191470166859777]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.keywordQuery/8390330_aop_timestamp1751851575753access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerQueryParam{"keyword":null,"country":"en","pageSize":10,"filter":null,"sort":null,"outMemberId":null,"priceStart":null,"priceEnd":null,"categoryId":null,"categoryIdList":null,"regionOpp":null,"productCollectionId":"262069983","snId":null,"keywordTranslate":null,"beginPage":1,"saleFilterList":null} 签名: 930A5A594A2413E2440085E4912E9C09494E2EF5
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 开始同步推荐商品到数据库，数量: 20
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191470166859776]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191470166859776]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 批量插入成功，预期: 20 条，实际: 20 条
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 推荐商品同步完成，新增: 20 条，更新: 0 条
2025-07-07 09:26:16 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-07 09:26:17 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 推荐商品结果已缓存, key: recommend:EN:1:20
2025-07-07 09:26:17 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859776]:[0] 缓存统计 - Key: recommend:EN:1:20, Hit: false
2025-07-07 09:26:17 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191470166859776]:[0] 成功加载热门商品，数量: 20，ID同步状态：正常
2025-07-07 09:26:17 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1094191470166859777]:[0] search result: GoodsSearchResponse.Result(success=true, code=200, message=null, result=GoodsSearchResponse.SearchResult(totalRecords=2000, totalPage=201, pageSize=10, currentPage=1, data=[GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jj690j1vJfYHdqU5R_!!*************-0-cib.jpg, subject=洗衣凝珠五合一超香水型持久留香珠除菌除螨强力去污浓缩型三盒一, subjectTrans=Laundry beads five-in-one super perfume type lasting fragrance beads sterilization mite removal strong decontamination concentrated type three boxes one, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.10, consignPrice=15.1, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=20%, monthSold=2709774, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[yx, select], tradeScore=4.1, whiteImage=null, promotionModel=null, topCategoryId=*********, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-05-11 09:56:38, modifyDate=2025-07-06 20:17:29, isSelect=true, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=2, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=5.0, offerExperienceScore=5.0, afterSalesExperienceScore=3.8, consultingExperienceScore=2.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDTIM2EKFGUUWSHJUZUIRKOIJLUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01eOj9s6219wo0NO3rW_!!**********-0-cib.jpg, subject=塑料地钉 户外天幕防风沙滩钉 帐篷配件野营帐篷地丁 螺纹帐篷钉, subjectTrans=Plastic ground nail outdoor canopy windproof beach nail tent accessories camping tent ground thread tent nail, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.05, consignPrice=4.05, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=39%, monthSold=2961108, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[powerful_merchants, tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=https://cbu01.alicdn.com/img/ibank/O1CN01hj1kzo1Bs2uuyw2of_!!0-0-cib.jpg, promotionModel=null, topCategoryId=18, secondCategoryId=281904, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-11-10 11:34:48, modifyDate=2025-07-06 12:00:56, isSelect=true, minOrderQuantity=2, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=5, compositeServiceScore=4.0, logisticsExperienceScore=3.5, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=3.7, consultingExperienceScore=2.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KVEECWKUI5HEUWKHLFNFISKOJJLEYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01gYjdMm1S86gKsJEax_!!*************-0-cib.jpg, subject=一次性医用外科口罩医用级独立包装不勒耳10个装三层防护白色口罩, subjectTrans=Disposable Medical Surgical Masks Medical Grade Independent Packaging 10 Pack Three-Layer Protective White Masks, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.07, consignPrice=2.57, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=16%, monthSold=********, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=4.8, whiteImage=null, promotionModel=null, topCategoryId=70, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2024-03-27 13:14:09, modifyDate=2025-07-06 04:56:06, isSelect=true, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.5, logisticsExperienceScore=3.5, disputeComplaintScore=5.0, offerExperienceScore=5.0, afterSalesExperienceScore=3.1, consultingExperienceScore=4.5, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2LBDTIM2UJVGVEVKHLFMUIT2NJJLEYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01YxC30h29QuF8RMSTZ_!!**********-0-cib.jpg, subject=磁力片积木儿童磁铁磁片拼图男孩女孩宝宝吸铁石贴益智拼装玩具方, subjectTrans=Magnetic Piece Building Blocks Children's Magnet Magnetic Piece Puzzle Boys Girls Baby Iron Piece Puzzle Assembled Toy Square, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.22, consignPrice=2.22, promotionPrice=0.21, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=12%, monthSold=1322832, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[powerful_merchants, tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=https://cbu01.alicdn.com/img/ibank/O1CN01PsZF2c1Bs2uwGFtro_!!0-0-cib.jpg, promotionModel=GoodsSearchResponse.PromotionModel(hasPromotion=true, promotionType=plus), topCategoryId=1813, secondCategoryId=181309, thirdCategoryId=*********, isPatentProduct=false, createDate=2022-03-27 17:30:56, modifyDate=2025-07-06 20:34:56, isSelect=true, minOrderQuantity=10, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.5, logisticsExperienceScore=4.0, disputeComplaintScore=5.0, offerExperienceScore=4.0, afterSalesExperienceScore=3.9, consultingExperienceScore=3.5, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTSSLBDUKMSEJ5HFEUKHJUZEIR2NIJJUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01gQulQn1r16x1vOhZl_!!*************-0-cib.jpg, subject=吸色片防串色洗衣片防串色吸色片防串色染色片染色片散装色母片, subjectTrans=Color Absorbing Film Anti-cross-color Laundry Film Anti-cross-color Absorbing Film Anti-cross-color Dyeing Film Dyeing Film Bulk Color Masterbatch, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.04, consignPrice=0.04, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=0%, monthSold=0, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=false, sellerIdentities=[tp_member], offerIdentities=[], tradeScore=, whiteImage=null, promotionModel=null, topCategoryId=*********, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2025-04-17 13:55:30, modifyDate=2025-06-27 02:18:19, isSelect=false, minOrderQuantity=100, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=3.5, logisticsExperienceScore=3.0, disputeComplaintScore=4.0, offerExperienceScore=0.0, afterSalesExperienceScore=3.3, consultingExperienceScore=2.0, repeatPurchasePercent=0.*****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMT2KKJDU2WKUIFHFUWKIIFMUIQ2PIJLUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jHPuFv1d3wiWFnnKa_!!*************-0-cib.jpg, subject=一次性手环现货儿童游乐场手腕带会展演唱会防水门票杜邦纸质腕带, subjectTrans=Disposable Bracelet Spot Children's Playground Wristband Exhibition Concert Waterproof Tickets DuPont Paper Wristband, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.04, consignPrice=5.04, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=14%, monthSold=********, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=null, topCategoryId=54, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2022-12-05 15:36:39, modifyDate=2025-07-07 06:10:19, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=4.5, logisticsExperienceScore=4.0, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=4.0, consultingExperienceScore=4.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTSSLJDVCWSEI5HEUVKHLFMVIRKNJJLEYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MhuR0Z1KKuisKG0hJ_!!*************-0-cib.jpg, subject=原创可爱镭射咕卡贴纸DIY明星贴纸文具贴纸装饰画手账贴纸批发, subjectTrans=Original cute laser goo card sticker DIY star sticker stationery sticker decorative painting hand account sticker wholesale, offerId=************, isJxhy=true, priceInfo=GoodsSearchResponse.PriceInfo(price=0.03, consignPrice=3.03, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=38%, monthSold=********, traceInfo=object_id%**************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=null, topCategoryId=67, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-04-01 20:45:15, modifyDate=2025-07-06 12:12:45, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.0, logisticsExperienceScore=3.0, disputeComplaintScore=4.0, offerExperienceScore=3.0, afterSalesExperienceScore=3.3, consultingExperienceScore=4.0, repeatPurchasePercent=0.***************), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDUCWSUIVHUEVSHIE2EIUKPJJJUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01ZU4IRJ1PMM2xLgZyi_!!*************-0-cib.jpg, subject=新款锌合金弹簧圈开口龙虾扣门扣钥匙扣版扣配件洞洞鞋大圈材料, subjectTrans=New zinc alloy spring ring open Lobster clasp door buckle keychain version buckle accessories hole shoes big ring material, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.25, consignPrice=3.25, promotionPrice=0.24, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=19%, monthSold=1425371, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[super_factory, tp_member], offerIdentities=[select], tradeScore=4.9, whiteImage=null, promotionModel=GoodsSearchResponse.PromotionModel(hasPromotion=true, promotionType=plus), topCategoryId=54, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-04-16 19:30:26, modifyDate=2025-07-07 02:28:07, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=4.0, logisticsExperienceScore=4.5, disputeComplaintScore=4.0, offerExperienceScore=2.0, afterSalesExperienceScore=4.0, consultingExperienceScore=3.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDUSNCEIFHEEUSHIUZUIUKNIJKEYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01cGSnZE2Jh4Ug7PpD5_!!*************-0-cib.jpg, subject=眼镜鼻托 通用型 气囊 硅胶 鼻梁架防滑托叶送螺丝刀眼镜鼻托批发, subjectTrans=Glasses Nose Pad Universal Airbag Silicone Nose Bridge Non-slip Holes Screwdriver Glasses Nose Pad Wholesale, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.10, consignPrice=2, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=33%, monthSold=2281727, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=4.7, whiteImage=null, promotionModel=null, topCategoryId=54, secondCategoryId=80, thirdCategoryId=*********, isPatentProduct=false, createDate=2023-04-09 19:45:56, modifyDate=2025-07-06 19:37:16, isSelect=true, minOrderQuantity=2, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=5.0, logisticsExperienceScore=4.0, disputeComplaintScore=4.0, offerExperienceScore=4.0, afterSalesExperienceScore=3.4, consultingExperienceScore=5.0, repeatPurchasePercent=0.****************), productSimpleShippingInfo=GoodsSearchResponse.ProductShippingInfo(shippingTimeGuarantee=shipIn48Hours), promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTS2KJDUKNCEKNGVUU2HGRNFIT2PIJLUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null), GoodsSearchResponse.GoodsInfo(imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01RJw6rx1SFR82dPicr_!!*************-0-cib.jpg, subject=红糖姜茶独立包装冲剂条装带日期速溶颗粒 姜茶带日期固体饮料, subjectTrans=Brown sugar ginger tea pack instant particles concentrated bulk independent pouch wholesale factory delivery ginger tea 10g, offerId=************, isJxhy=false, priceInfo=GoodsSearchResponse.PriceInfo(price=0.14, consignPrice=2.44, promotionPrice=null, jxhyPrice=null, pfJxhyPrice=null), repurchaseRate=31%, monthSold=8110560, traceInfo=object_id%40************%5Eobject_type%40offer%5Etrace%40213e02e617518515764092271e0d40%5Ekeyword%40null%5EoutMemberId%40null, isOnePsale=true, sellerIdentities=[tp_member], offerIdentities=[select], tradeScore=5.0, whiteImage=null, promotionModel=null, topCategoryId=2, secondCategoryId=*********, thirdCategoryId=*********, isPatentProduct=false, createDate=2020-11-27 19:45:55, modifyDate=2025-07-04 05:22:06, isSelect=true, minOrderQuantity=1, sellerDataInfo=GoodsSearchResponse.SellerDataInfo(tradeMedalLevel=2, compositeServiceScore=4.5, logisticsExperienceScore=5.0, disputeComplaintScore=5.0, offerExperienceScore=4.0, afterSalesExperienceScore=3.6, consultingExperienceScore=5.0, repeatPurchasePercent=0.4373059498171905), productSimpleShippingInfo=null, promotionURL=https://detail.1688.com/offer/************.html?fromkv=refer:HVAVUVCTJVBFIR2NLFDDMTSSKRDU2WKUJ5GUEUSHIUZFISKOKJJUYNKRLBATESZXI5CTGVCLJVFFUR2NGNKFGTS2K5EEKMSUKNMDGM2QKU6T2PJ5HVEA1111&kjSource=pc, token=null)]))
2025-07-07 09:26:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 开始批量同步搜索结果商品数据，数量: 10
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 提取商品ID完成，耗时: 1ms
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191470166859777]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:17 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191470166859776]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:17 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191470166859776]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 查询已存在数据完成，耗时: 13ms，已存在: 10 条
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094191470166859777]:[0] 使用预设ID: 706173138497600 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-8] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - 使用预设ID: 705814291476481 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - 使用预设ID: 705814291476490 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-3] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - 使用预设ID: 706173138501698 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-5] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - 使用预设ID: *************** 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-6] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094191470166859777]:[0] 使用预设ID: 705814291476486 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-4] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - 使用预设ID: 706582547877893 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094191470166859777]:[0] 使用预设ID: 706943950012441 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-9] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094191470166859777]:[0] 使用预设ID: 705814291476487 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [ForkJoinPool.commonPool-worker-7] [tid::uId::ip::os::browser:] c.f.s.d.c.product.PdcProductSearchConvertMapping - [1094191470166859777]:[0] 使用预设ID: 705814291476485 对应平台商品ID: ************
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 数据处理完成，耗时: 6ms，新增: 0 条，更新: 10 条
2025-07-07 09:26:17 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191470166859776]:[0] 清理增强租户上下文
2025-07-07 09:26:17 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859776]:[0] 过滤器清理租户上下文完成
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191470166859777]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 批量更新成功，预期: 10 条，实际: 10 条
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 数据库操作完成，耗时: 69ms，影响行数: 10
2025-07-07 09:26:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 单批次同步完成，总数: 10, 影响: 10 条，总耗时: 89ms
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 更新多层缓存完成, fullKey: search:65d43bf503e3410204fa882720c56e62, baseKey: search:27eac5f609a7339af532c14c7e27214e:nopag
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 缓存统计 - Key: search:65d43bf503e3410204fa882720c56e62, Hit: false
2025-07-07 09:26:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191470166859777]:[0] 成功搜索电子产品，数量: 10
2025-07-07 09:26:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191470166859777]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191470166859777]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191470166859777]:[0] 清理增强租户上下文
2025-07-07 09:26:17 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191470166859777]:[0] 过滤器清理租户上下文完成
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191481227239424]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191481227239424]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191481227239424]:[0] 租户缓存命中: 10000
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191481227239424]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191481227239424]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191481227239424]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/products/***************)
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191481227239424]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191481227239424]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191481227239424]:[0] auth check result: true
2025-07-07 09:26:18 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191481227239424]:[0] [GET] /api/products/***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191481227239424]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=2/100
2025-07-07 09:26:18 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.controller.ProductController - [1094191481227239424]:[0] 获取商品详情: productId=***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094191481227239424]:[0] 获取商品详情VO: 传入ID=***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191481227239424]:[0] 获取或同步产品数据: platformProductId=***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191481227239424]:[0] TzProductSpu数据已过期，需要重新同步: spuId=711254625165514, 最后更新时间=2025-06-25T19:02:28, 距今11天
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191481227239424]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191481227239424]:[0] 数据库不存在，从API获取商品详情, id: ***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191481227239424]:[0] 从API获取商品详情, offerId: *************** , platformProductId: ************
2025-07-07 09:26:18 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191481227239424]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1751851578317access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerDetailParam{"offerId":************,"country":"en","outMemberId":null} 签名: F20C18B2DC7101FF9DDA0B616C0702FC44999C8E
2025-07-07 09:26:18 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094191481227239424]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 09:26:18 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1094191470166859777]:[0] result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=*********, categoryName=null, subject=一次性医用外科口罩医用级独立包装不勒耳10个装三层防护白色口罩, subjectTrans=Disposable Medical Surgical Masks Medical Grade Independent Packaging 10 Pack Three-Layer Protective White Masks, description=<div id="offer-template-0"></div><div class="ruifei_qdzs_1" style="margin: 0 auto;width: 790.0px;">
<table border="0" cellpadding="0" cellspacing="0" style="background-size: 100.0% 100.0%;margin-bottom: -4.0px;width: 100.0%;">
<tbody>
<tr align="center" valign="middle">
<td><img style="width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/2020/145/407/22582704541_2056063352.jpg"/></td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="0" cellspacing="0" style="background-color: #f4da70;width: 100.0%;">
<tbody>
<tr>
<td style="line-height: 10.0px;">&nbsp;</td>
</tr>
<tr>
<td style="width: 5.0px;"></td>
<td style="vertical-align: bottom;width: 195.0px;">
<div style="padding: 0 5.0px;">
<div style="width: 185.0px;height: 185.0px;background: #ffffff;display: flex;justify-content: center;align-items: center;aspect-ratio: 1 / 1;"><a class="product_1_productImage_url" style="text-decoration: none;display: contents;" href="https://detail.1688.com/offer/************.html" target="_blank"><img id="product_1_productImage" style="max-width: 100.0%;max-height: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01uNHdC91S86flIw98w_!!*************-0-cib.580x580.jpg"/></a></div>
<div style="height: 50.0px;background-color: #f7b54d;display: flex;font-size: 14.0px;">
<div style="width: 50.0%;padding-top: 12.0px;text-align: left;"><span style="font-size: 18.0px;color: #ffffff;line-height: 25.0px;margin-left: 10.0px;vertical-align: middle;"><em style="font-style: normal;vertical-align: super;">￥</em><span id="product_1_productPrice" style="font-weight: 600;display: inline-block;width: 60.0%;overflow: hidden;white-space: nowrap;">0.05</span></span></div>
<div style="width: 50.0%;"><a class="product_1_productImage_url" style="text-decoration: none;background: #ffffff;display: flex;justify-content: center;align-items: center;margin: 10.0px auto;width: 80.0%;border-radius: 15.0px;" href="https://detail.1688.com/offer/************.html" target="_blank"><span style="color: #f7b54d;line-height: 30.0px;">立即抢购</span></a></div>
</div>
</div>
</td>
<td style="vertical-align: bottom;width: 195.0px;">
<div style="padding: 0 5.0px;">
<div style="width: 185.0px;height: 185.0px;background: #ffffff;display: flex;justify-content: center;align-items: center;aspect-ratio: 1 / 1;"><a class="product_2_productImage_url" style="text-decoration: none;display: contents;" href="https://detail.1688.com/offer/************.html" target="_blank"><img id="product_2_productImage" style="max-width: 100.0%;max-height: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01gYjdMm1S86gKsJEax_!!*************-0-cib.580x580.jpg"/></a></div>
<div style="height: 50.0px;background-color: #f7b54d;display: flex;font-size: 14.0px;">
<div style="width: 50.0%;padding-top: 12.0px;text-align: left;"><span style="font-size: 18.0px;color: #ffffff;line-height: 25.0px;margin-left: 10.0px;vertical-align: middle;"><em style="font-style: normal;vertical-align: super;">￥</em><span id="product_2_productPrice" style="font-weight: 600;display: inline-block;width: 60.0%;overflow: hidden;white-space: nowrap;">0.04</span></span></div>
<div style="width: 50.0%;"><a class="product_2_productImage_url" style="text-decoration: none;background: #ffffff;display: flex;justify-content: center;align-items: center;margin: 10.0px auto;width: 80.0%;border-radius: 15.0px;" href="https://detail.1688.com/offer/************.html" target="_blank"><span style="color: #f7b54d;line-height: 30.0px;">立即抢购</span></a></div>
</div>
</div>
</td>
<td style="vertical-align: bottom;width: 195.0px;">
<div style="padding: 0 5.0px;">
<div style="width: 185.0px;height: 185.0px;background: #ffffff;display: flex;justify-content: center;align-items: center;aspect-ratio: 1 / 1;"><a class="product_3_productImage_url" style="text-decoration: none;display: contents;" href="https://detail.1688.com/offer/************.html" target="_blank"><img id="product_3_productImage" style="max-width: 100.0%;max-height: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01MXjaAU1S86guajC5J_!!*************-0-cib.580x580.jpg"/></a></div>
<div style="height: 50.0px;background-color: #f7b54d;display: flex;font-size: 14.0px;">
<div style="width: 50.0%;padding-top: 12.0px;text-align: left;"><span style="font-size: 18.0px;color: #ffffff;line-height: 25.0px;margin-left: 10.0px;vertical-align: middle;"><em style="font-style: normal;vertical-align: super;">￥</em><span id="product_3_productPrice" style="font-weight: 600;display: inline-block;width: 60.0%;overflow: hidden;white-space: nowrap;">0.04</span></span></div>
<div style="width: 50.0%;"><a class="product_3_productImage_url" style="text-decoration: none;background: #ffffff;display: flex;justify-content: center;align-items: center;margin: 10.0px auto;width: 80.0%;border-radius: 15.0px;" href="https://detail.1688.com/offer/************.html" target="_blank"><span style="color: #f7b54d;line-height: 30.0px;">立即抢购</span></a></div>
</div>
</div>
</td>
<td style="vertical-align: bottom;width: 195.0px;">
<div style="padding: 0 5.0px;">
<div style="width: 185.0px;height: 185.0px;background: #ffffff;display: flex;justify-content: center;align-items: center;aspect-ratio: 1 / 1;"><a class="product_4_productImage_url" style="text-decoration: none;display: contents;" href="https://detail.1688.com/offer/************.html" target="_blank"><img id="product_4_productImage" style="max-width: 100.0%;max-height: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01MhkHxr1S86fqNy0M4_!!*************-0-cib.580x580.jpg"/></a></div>
<div style="height: 50.0px;background-color: #f7b54d;display: flex;font-size: 14.0px;">
<div style="width: 50.0%;padding-top: 12.0px;text-align: left;"><span style="font-size: 18.0px;color: #ffffff;line-height: 25.0px;margin-left: 10.0px;vertical-align: middle;"><em style="font-style: normal;vertical-align: super;">￥</em><span id="product_4_productPrice" style="font-weight: 600;display: inline-block;width: 60.0%;overflow: hidden;white-space: nowrap;">0.03</span></span></div>
<div style="width: 50.0%;"><a class="product_4_productImage_url" style="text-decoration: none;background: #ffffff;display: flex;justify-content: center;align-items: center;margin: 10.0px auto;width: 80.0%;border-radius: 15.0px;" href="https://detail.1688.com/offer/************.html" target="_blank"><span style="color: #f7b54d;line-height: 30.0px;">立即抢购</span></a></div>
</div>
</div>
</td>
<td style="width: 5.0px;"></td>
</tr>
<tr>
<td style="line-height: 10.0px;">&nbsp;</td>
</tr>
</tbody>
</table>
</div>
<p style="text-align: center;"><span style="font-size: 22.0pt;color: #ff0000;"><strong>本店所有商品均不含税 开票联系客服</strong></span></p>
<p style="text-align: center;"><span style="font-size: 22.0pt;color: #ff0000;"><strong>店内商品未备注新日期的：均为老日期或临期商品。介意勿拍！拍下表示默认！</strong></span></p>
<p><strong style="color: #ff0000;font-size: 22.0pt;">1.口罩一包10支，下单请拍100支，数字错误不发货！</strong></p>
<p style="text-align: left;"><strong style="color: #ff0000;font-size: 22.0pt;text-align: center;">2.工厂直销，医用卫材，无质量问题一经售出，不支持退换，慎拍！</strong></p>
<p style="text-align: left;"><span style="font-size: 22.0pt;color: #ff0000;"><strong style="font-size: 22.0pt;"><img src="https://cbu01.alicdn.com/img/ibank/O1CN01RLeZhP1S86fBZjL9k_!!*************-0-cib.jpg?__r__=*************" alt="、_01"/></strong></span></p>
<p><span style="font-size: 22.0pt;color: #ff0000;"><strong><img src="https://cbu01.alicdn.com/img/ibank/O1CN015QJRy31S86f5WwDxB_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01VkjN771S86f9RecfL_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01Zu7reB1S86f7RacTY_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01qLSnnj1S86f3G7v47_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01J0SdyV1S86f5cTKy0_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01PSUe5E1S86fADrJiX_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01bTSkLj1S86f7RcMaK_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN01Bv7wJd1S86fADrzJE_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><img src="https://cbu01.alicdn.com/img/ibank/O1CN017aNWwD1S86f2COva0_!!*************-0-cib.jpg?__r__=*************" alt="一键复制_20240125112852"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/><br class="img-brk"/></strong></span></p>, mainVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01gYjdMm1S86gKsJEax_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN0196dNo41S86fVa7sqo_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01TiQWnk1S86gAB6TT2_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01mtfzFU1S86fXHfHji_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01JmH5kD1S86fYYx4Pl_!!*************-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=豫世安 豫金蓝, attributeNameTrans=Brand, valueTrans=Yu shi an yu jin lan), GoodsDetailResponse.ProductAttribute(attributeId=287, attributeName=材质, value=无纺布+熔喷布, attributeNameTrans=Material, valueTrans=Non-woven fabric + meltblown fabric), GoodsDetailResponse.ProductAttribute(attributeId=7592, attributeName=用途范围, value=工作、外出、日常防护, attributeNameTrans=Scope of use, valueTrans=Work, going out, daily protection), GoodsDetailResponse.ProductAttribute(attributeId=3237, attributeName=药（械）准字, value=豫械注准***********, attributeNameTrans=Drug (device) approved word, valueTrans=Yu machinery note ***********), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【10支/包】外科级蓝色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[10 count/pack] surgical grade blue (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【10支/包】儿童外科蓝色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[10 pieces/pack] children's surgical blue (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【10支/包】外科级白色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[10 count/pack] surgical grade white (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【10支/包】外科级蓝色 （新日期）, attributeNameTrans=Specifications and models, valueTrans=[10 count/pack] surgical grade blue (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【10支独立】外科级蓝色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[10 count] surgical grade blue (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【独立包装】外科级蓝色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[individually packaged] surgical grade blue (new date)), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=【独立盒装】外科级蓝色（新日期）, attributeNameTrans=Specifications and models, valueTrans=[individual boxed] surgical grade blue (new date))], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=********, price=0.07, jxhyPrice=null, skuId=*************, specId=d5576e00b1a0d23ae5d976eb65c3f30e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【10支/包】外科级蓝色（新日期）, valueTrans=[10 pieces/pack] surgical grade blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01OO63K81S86misLyBu_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.07, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.57, offerPrice=0.07)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=1487360, price=0.07, jxhyPrice=null, skuId=*************, specId=c550cbdc8167bacdaea6cf4249f1792e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【10支/包】儿童外科蓝色（新日期）, valueTrans=[10 pieces/pack] children's surgical blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01VJEZaW1S86fUpGZ74_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.07, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.57, offerPrice=0.07)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=********, price=0.08, jxhyPrice=null, skuId=*************, specId=0863a58425d8c0ca9c54c807acb0c90e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【10支/包】外科级白色（新日期）, valueTrans=[10 pieces/pack] surgical grade white (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN017Gpny11S86mkOLisQ_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.08, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.58, offerPrice=0.08)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=********, price=0.08, jxhyPrice=null, skuId=*************, specId=caef3ea0f18d3ae002bbd9f90956bf1c, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【10支/包】外科级蓝色 （新日期）, valueTrans=[10 pieces/pack] surgical grade blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01O3j36p1S86mgDBJwQ_!!*************-0-cib.jpg)], cargoNumber=升级款, promotionPrice=null, consignPrice=0.08, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.58, offerPrice=0.08)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9535450, price=0.09, jxhyPrice=null, skuId=*************, specId=d593c9475eb29b5a488b8e92196d623a, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【10支独立】外科级蓝色（新日期）, valueTrans=[10 count] surgical grade blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01EJa1Tz1S86mvZcdPr_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.09, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.59, offerPrice=0.09)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=********, price=0.11, jxhyPrice=null, skuId=*************, specId=9dcb8ff2727bd629d3624d999aa7c40e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【独立包装】外科级蓝色（新日期）, valueTrans=[individually packaged] surgical grade blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN018L09b51S86mnxhCvk_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.11, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.61, offerPrice=0.11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=********, price=0.12, jxhyPrice=null, skuId=*************, specId=976c3bd8418d4adbe81a17359e303d39, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=Specifications and models, value=【独立盒装】外科级蓝色（新日期）, valueTrans=[independent boxed] surgical blue (new date), skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01lNKlzO1S86mulzkxl_!!*************-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=0.12, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=2.62, offerPrice=0.12))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=*********, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=100, price=0.07, promotionPrice=null)], quoteType=1, unitInfo=GoodsDetailResponse.UnitInfo(unit=支, transUnit=Support), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=true, startQuantity=2, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=河南省新乡市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=shipIn48Hours, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=d5576e00b1a0d23ae5d976eb65c3f30e, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=c550cbdc8167bacdaea6cf4249f1792e, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=0863a58425d8c0ca9c54c807acb0c90e, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=caef3ea0f18d3ae002bbd9f90956bf1c, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=d593c9475eb29b5a488b8e92196d623a, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=9dcb8ff2727bd629d3624d999aa7c40e, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1), GoodsDetailResponse.SkuShippingInfo(specId=976c3bd8418d4adbe81a17359e303d39, skuId=*************, width=1.0, length=1.0, height=1.0, weight=1)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=1.0, length=1.0, height=1.0, weight=0.001, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=true, sellerOpenId=BBBNCwBDR6QvfTKo49zsdKSzg, minOrderQuantity=100, batchNumber=100, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=true), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=true), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=true), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false), GoodsDetailResponse.TagInfoList(key=isQqyx, value=true)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=GoodsDetailResponse.SellerMixSetting(generalHunpi=true, mixAmount=2000, mixNumber=20000), productCargoNumber=null, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=3, compositeServiceScore=4.5, logisticsExperienceScore=3.5, disputeComplaintScore=5.0, offerExperienceScore=5.0, consultingExperienceScore=4.5, repeatPurchasePercent=0.****************, afterSalesExperienceScore=3.1, collect30DayWithin48HPercent=0.9850746268656716, qualityRefundWithin30Day=0.0), soldOut=3552001, channelPrice=null, promotionModel=null, tradeScore=4.8, topCategoryId=70, secondCategoryId=*********, thirdCategoryId=*********, sellingPoint=[Disposable Medical Grade:These masks are disposable medical grade, ensuring high levels of protection and quality for healthcare professionals., Independent Packaging:The masks come in individual independent packaging, ensuring each mask is fresh and ready for use when needed., Comfortable Fit:These masks are designed to be comfortable to wear, avoiding discomfort and pain in the ears during prolonged use., Three-Layer Protection:The masks provide three layers of protection, providing comprehensive coverage and safety against infectious diseases., White Color:The masks are in a neutral white color, making them versatile and suitable for various healthcare settings., Bulk Pack:This product comes in a pack of 10, providing value for money and ensuring you have backup masks when needed.], offerIdentities=[tp_member], createDate=2024-03-27 13:14:09, isSelect=false, certificateList=[GoodsDetailResponse.CertificateList(certificateName=医疗器械注册证, certificateCode=豫械注准***********, certificatePhotoList=[https://img.china.alibaba.com/img/ibank/O1CN01pvxxKB1Bs2v6PYjLJ_!!0-0-cib.jpg])], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.d.c.product.PdcProductDetailConvertMapping - [1094191481227239424]:[0] 使用预设ID: *************** 对应平台商品ID: ************
2025-07-07 09:26:18 INFO  [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - [1094191470166859777]:[0] 开始解密旺旺昵称, openUid: BBBNCwBDR6QvfTKo49zsdKSzg
2025-07-07 09:26:18 INFO  [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191470166859777]:[0] 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1751851578665access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2openUidBBBNCwBDR6QvfTKo49zsdKSzg 签名: 4FAF96EFECC2C7BD6CD7DD1BD265E23477FE7EED
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.common.util.MetaInfoHashUtils - [1094191481227239424]:[0] 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=00d44e7f521bbcc3b73a4994b09bad9f, metaInfo长度=19696
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191481227239424]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191481227239424]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191481227239424]:[0] 商品详情同步到数据库完成, id: ***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191481227239424]:[0] 商品详情更新到缓存完成, id: ***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191481227239424]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191481227239424]:[0] 从PdcProductMapping同步产品数据: platformProductId=***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191481227239424]:[0] 开始同步产品数据，platformProductId: ***************
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191481227239424]:[0] SPU已存在（基于平台产品ID），直接返回，spuId: 711254625165514
2025-07-07 09:26:18 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 从缓存获取商品详情, id: ***************
2025-07-07 09:26:18 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191481227239424]:[0] [GET] /api/products/*************** 200 573ms
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191481227239424]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191481227239424]:[0] 清理增强租户上下文
2025-07-07 09:26:18 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191481227239424]:[0] 过滤器清理租户上下文完成
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191551070789632]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191551158870016]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191551070789632]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191551070789632]:[0] 租户缓存命中: 10000
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551070789632]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191551070789632]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551070789632]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191551070789632]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191551070789632]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191551070789632]:[0] auth check result: true
2025-07-07 09:26:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191551070789632]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191551070789632]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=3/100
2025-07-07 09:26:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191551070789632]:[0] 搜索电子产品（带缓存和同步入库）
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551070789632]:[0] 命中完整缓存, key: search:65d43bf503e3410204fa882720c56e62
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551070789632]:[0] 缓存统计 - Key: search:65d43bf503e3410204fa882720c56e62, Hit: true
2025-07-07 09:26:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191551070789632]:[0] 成功搜索电子产品，数量: 10
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191551158870016]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191551158870016]:[0] 租户缓存命中: 10000
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551158870016]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191551158870016]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551158870016]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191551158870016]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191551158870016]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191551070789632]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191551158870016]:[0] auth check result: true
2025-07-07 09:26:34 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191551158870016]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191551070789632]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191551070789632]:[0] 清理增强租户上下文
2025-07-07 09:26:34 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551070789632]:[0] 过滤器清理租户上下文完成
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191551158870016]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=4/100
2025-07-07 09:26:34 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191551158870016]:[0] 加载热门商品列表（带缓存和同步入库）
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551158870016]:[0] 开始获取推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551158870016]:[0] 推荐商品缓存获取开始，语言: EN, 页码: 1, 强制刷新: false
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551158870016]:[0] 命中推荐商品缓存, key: recommend:EN:1:20
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191551158870016]:[0] 缓存统计 - Key: recommend:EN:1:20, Hit: true
2025-07-07 09:26:34 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191551158870016]:[0] 成功加载热门商品，数量: 20，ID同步状态：正常
2025-07-07 09:26:34 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191551158870016]:[0] [GET] /api/home/<USER>
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191551158870016]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191551158870016]:[0] 清理增强租户上下文
2025-07-07 09:26:34 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191551158870016]:[0] 过滤器清理租户上下文完成
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191562290552832]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191562290552832]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191562290552832]:[0] 租户缓存命中: 10000
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191562290552832]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191562290552832]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191562290552832]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/products/***************)
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191562290552832]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191562290552832]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191562290552832]:[0] auth check result: true
2025-07-07 09:26:37 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191562290552832]:[0] [GET] /api/products/***************
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191562290552832]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=5/100
2025-07-07 09:26:37 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.controller.ProductController - [1094191562290552832]:[0] 获取商品详情: productId=***************
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.ProductServiceImpl - [1094191562290552832]:[0] 获取商品详情VO: 传入ID=***************
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191562290552832]:[0] 获取或同步产品数据: platformProductId=***************
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191562290552832]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191562290552832]:[0] 数据库不存在，从API获取商品详情, id: ***************
2025-07-07 09:26:37 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191562290552832]:[0] 从API获取商品详情, offerId: *************** , platformProductId: ************
2025-07-07 09:26:37 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191562290552832]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1751851597607access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2offerDetailParam{"offerId":************,"country":"en","outMemberId":null} 签名: AEBFBD16F99720BF308FAFF2281DDA831541D03C
2025-07-07 09:26:38 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1094191470166859777]:[0] result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=1046714, categoryName=null, subject=powerbank充电宝超大容量50000毫安快充自带线便携移动电源定 制, subjectTrans=powerbank charging treasure large capacity 50000 mA fast charging with portable mobile power supply customization, description=<div id="offer-template-0"></div><div style="width: 790.0px;">
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01vnWjrh1ZjOL5v6lDR_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_0"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN015Y5qQt1ZjOKz2oW0E_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_1"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01klYfYB1ZjOL42HGDe_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_2"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01DYKxu41ZjOL6Wde9N_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_3"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01nSEmKN1ZjOL67eZWj_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_4"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN019rv0Vx1ZjOL5fKrDo_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_5"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01XNjl9k1ZjOL45x6gL_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_6"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01FT5tWF1ZjOL6rVF5B_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_7"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01VGSxaq1ZjOL5uIqvR_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_8"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01P2QQhh1ZjOL64ybca_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_9"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01jMWUXz1ZjOKzmz7mw_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_10"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01aqMqQU1ZjOL5uuSdg_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_11"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01PmguQp1ZjOL64yo6s_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_12"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN013IuRrG1ZjOL4mOJOp_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_13"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01KgWcow1ZjOL5uKfET_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_14"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN017w58w51ZjOL45yRtZ_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_15"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01qxb3bL1ZjOL7ETyjU_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_16"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01dwxW7t1ZjOL7GLrBn_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_17"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01dWaPSL1ZjOL6evswi_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_18"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN0197hAce1ZjOL5uLCWI_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_19"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01qhRjpY1ZjOL4exQsF_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_20"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01T3KSXa1ZjOL7ETNK4_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_21"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01no8u7h1ZjOL6rTy6m_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_22"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN013U4UvF1ZjOL6etbYH_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_23"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01O7G9dG1ZjOL5uK7yv_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_24"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01k2h1QE1ZjOL6rTy7X_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_25"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01bP8k8I1ZjOL7lARCT_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_26"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN017vXXYg1ZjOKzmxFNl_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_27"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN017t4m1e1ZjOL7GOTPu_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_28"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01uMUgO91ZjOL4mNAlp_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_29"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN010N3ld01ZjOL4mNqLI_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_30"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01j0ahEB1ZjOL6Rhper_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_31"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01IyMMSY1ZjOL887YbR_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_32"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01CC8rwR1ZjOKzmyWPR_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_33"/>
        
        
    
        
         <img src="https://cbu01.alicdn.com/img/ibank/O1CN01aLgBz11ZjOL6YPVSq_!!*************-0-cib.jpg" style="display: block;width: 100.0%;height: auto;" usemap="#_sdmap_34"/>
        
        
    </div>, mainVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01dFyv6s1ZjOLzi8r1r_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01WQTTg11ZjOL86y3WV_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01E2jeYR1ZjOL6dgD74_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01IBECW51ZjOL870k1g_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01ToCl111ZjOL6dh5Me_!!*************-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=UPINGZI, attributeNameTrans=Brand, valueTrans=UPINGZI), GoodsDetailResponse.ProductAttribute(attributeId=5243, attributeName=分类, value=充电宝, attributeNameTrans=Sort, valueTrans=Power bank), GoodsDetailResponse.ProductAttribute(attributeId=1398, attributeName=货号, value=MK-653, attributeNameTrans=Item number, valueTrans=MK-653), GoodsDetailResponse.ProductAttribute(attributeId=671, attributeName=电池类型, value=锂离子电池, attributeNameTrans=Battery type, valueTrans=Lithium ion battery), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=电芯类型, value=聚合物锂离子电芯, attributeNameTrans=Battery type, valueTrans=Polymer lithium-ion battery cell), GoodsDetailResponse.ProductAttribute(attributeId=2948, attributeName=外壳材质, value=塑料, attributeNameTrans=Shell material, valueTrans=Plastic), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=酷炫黑母舰, attributeNameTrans=Color, valueTrans=Cool black mother ship), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=中国红母舰, attributeNameTrans=Color, valueTrans=Chinese red mother ship), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=酷炫黑大霸王, attributeNameTrans=Color, valueTrans=Cool black overlord), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=皓月白大霸王, attributeNameTrans=Color, valueTrans=Haoyuebai the great overlord), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=太空黑带挂绳款, attributeNameTrans=Color, valueTrans=Space black belt lanyard model), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=冰雪白带挂绳款, attributeNameTrans=Color, valueTrans=Ice and snow white belt lanyard style), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=酷炫黑自带线大疆, attributeNameTrans=Color, valueTrans=Cool black comes with cable dji), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=中国红自带线大疆, attributeNameTrans=Color, valueTrans=China red comes with line dji), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=酷炫黑大疆, attributeNameTrans=Color, valueTrans=Cool black dji), GoodsDetailResponse.ProductAttribute(attributeId=3216, attributeName=颜色, value=中国红大疆, attributeNameTrans=Color, valueTrans=China red dji), GoodsDetailResponse.ProductAttribute(attributeId=********, attributeName=电源容量, value=50000mAh, attributeNameTrans=Power supply capacity, valueTrans=50000mAh), GoodsDetailResponse.ProductAttribute(attributeId=********, attributeName=电源容量, value=80000mAh, attributeNameTrans=Power supply capacity, valueTrans=80000mAh), GoodsDetailResponse.ProductAttribute(attributeId=********, attributeName=电源容量, value=100000mAh, attributeNameTrans=Power supply capacity, valueTrans=100000mAh), GoodsDetailResponse.ProductAttribute(attributeId=446, attributeName=尺寸, value=165*81*40mm, attributeNameTrans=Size, valueTrans=165*81*40mm), GoodsDetailResponse.ProductAttribute(attributeId=143220385, attributeName=是否专利货源, value=否, attributeNameTrans=Is it a patented source of goods?, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=101121517, attributeName=充电功率, value=10W, attributeNameTrans=Charging power, valueTrans=10W), GoodsDetailResponse.ProductAttribute(attributeId=401, attributeName=风格, value=国潮风, attributeNameTrans=Style, valueTrans=National trend), GoodsDetailResponse.ProductAttribute(attributeId=9069, attributeName=生产厂商, value=明科数码, attributeNameTrans=Manufacturer, valueTrans=Mingke digital), GoodsDetailResponse.ProductAttribute(attributeId=844, attributeName=额定容量, value=10000, attributeNameTrans=Rated capacity, valueTrans=10000), GoodsDetailResponse.ProductAttribute(attributeId=2678, attributeName=输入电压, value=5, attributeNameTrans=Input voltage, valueTrans=5), GoodsDetailResponse.ProductAttribute(attributeId=673, attributeName=电池容量, value=10000, attributeNameTrans=Battery capacity, valueTrans=10000), GoodsDetailResponse.ProductAttribute(attributeId=3844, attributeName=最大输出功率, value=10, attributeNameTrans=Maximum output power, valueTrans=10), GoodsDetailResponse.ProductAttribute(attributeId=716, attributeName=电压, value=5, attributeNameTrans=Voltage, valueTrans=5), GoodsDetailResponse.ProductAttribute(attributeId=125790829, attributeName=3C证书编号, value=2024260914000832, attributeNameTrans=3c certificate number, valueTrans=2024260914000832), GoodsDetailResponse.ProductAttribute(attributeId=177508229, attributeName=充电协议, value=QC2.0, attributeNameTrans=Charging protocol, valueTrans=QC2.0), GoodsDetailResponse.ProductAttribute(attributeId=517392460, attributeName=是否仅供跨境, value=否, attributeNameTrans=Is it only for cross-border use?, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=3151, attributeName=型号, value=C20000, attributeNameTrans=Model, valueTrans=C20000)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=51.0, jxhyPrice=null, skuId=*************, specId=f96c473203e61d503836077ba32a0a82, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑母舰, valueTrans=Cool black mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01vHhhGK1ZjOKzm5P0w_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=51.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=51)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=79.0, jxhyPrice=null, skuId=*************, specId=bac887cb029926ee5fa29735f13edde4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑母舰, valueTrans=Cool black mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01vHhhGK1ZjOKzm5P0w_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=79.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=79)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=95.0, jxhyPrice=null, skuId=*************, specId=2789a1a5b53bcce942836790ed02e4c9, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑母舰, valueTrans=Cool black mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01vHhhGK1ZjOKzm5P0w_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=95.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=95)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=51.0, jxhyPrice=null, skuId=*************, specId=c8bb3edc5c17339a4902dbac3a418071, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红母舰, valueTrans=Chinese red mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01GpkJLZ1ZjOL641Bw5_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=51.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=51)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=79.0, jxhyPrice=null, skuId=*************, specId=b8b4c9731506e53b0a0825f894c72657, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红母舰, valueTrans=Chinese red mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01GpkJLZ1ZjOL641Bw5_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=79.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=79)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=95.0, jxhyPrice=null, skuId=*************, specId=754a5a8384ce95f3e4127d436c020c9d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红母舰, valueTrans=Chinese red mother ship, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01GpkJLZ1ZjOL641Bw5_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=95.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=95)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=51.0, jxhyPrice=null, skuId=*************, specId=b8b292ae2cb12550a0b12384ce427531, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大霸王, valueTrans=Cool black overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN019mJzF31ZjOL87FNUY_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=51.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=51)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9997, price=79.0, jxhyPrice=null, skuId=*************, specId=fa7fb996764fe5d28b31237666184e57, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大霸王, valueTrans=Cool black overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN019mJzF31ZjOL87FNUY_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=79.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=79)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=95.0, jxhyPrice=null, skuId=*************, specId=1dbfc2b0ab92e4969b50d1560f53b706, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大霸王, valueTrans=Cool black overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN019mJzF31ZjOL87FNUY_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=95.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=95)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=51.0, jxhyPrice=null, skuId=*************, specId=2654f0b508b5c7f40177b9c2e0ba91bb, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=皓月白大霸王, valueTrans=Haoyuebai the great overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01pk0Bg01ZjOL87EEoL_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=51.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=51)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=79.0, jxhyPrice=null, skuId=*************, specId=8164fd83752c912437c4cd1b69f346fa, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=皓月白大霸王, valueTrans=Haoyuebai the great overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01pk0Bg01ZjOL87EEoL_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=79.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=79)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=95.0, jxhyPrice=null, skuId=*************, specId=0e008c554633f27ac33cb7ff23285b35, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=皓月白大霸王, valueTrans=Haoyuebai the great overlord, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01pk0Bg01ZjOL87EEoL_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=95.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=95)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=64.0, jxhyPrice=null, skuId=*************, specId=e3d4a160c75795bd6768db50b38eae96, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=太空黑带挂绳款, valueTrans=Space black belt lanyard model, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MITRRV1ZjOL5eRjxE_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=64.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=64)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=89.0, jxhyPrice=null, skuId=*************, specId=534c5cfd6b0abd38a963103e2f42ab1f, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=太空黑带挂绳款, valueTrans=Space black belt lanyard model, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MITRRV1ZjOL5eRjxE_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=89.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=89)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=101.0, jxhyPrice=null, skuId=*************, specId=9749ce1b6610fc990c713f9bc5baaf6e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=太空黑带挂绳款, valueTrans=Space black belt lanyard model, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MITRRV1ZjOL5eRjxE_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=101.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=101)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=64.0, jxhyPrice=null, skuId=*************, specId=a363520837f5f720b54c7379a23c3fc7, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=冰雪白带挂绳款, valueTrans=Ice and snow white belt lanyard style, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01Zepl3O1ZjOL6Qoyxs_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=64.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=64)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=89.0, jxhyPrice=null, skuId=*************, specId=c1f95bacfbcf5a3de52dd0bba096713d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=冰雪白带挂绳款, valueTrans=Ice and snow white belt lanyard style, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01Zepl3O1ZjOL6Qoyxs_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=89.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=89)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=101.0, jxhyPrice=null, skuId=*************, specId=9f5358fbfcf8c8a2190f1cf95c1d2715, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=冰雪白带挂绳款, valueTrans=Ice and snow white belt lanyard style, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01Zepl3O1ZjOL6Qoyxs_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=101.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=101)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=64.0, jxhyPrice=null, skuId=*************, specId=d14451afb4eaf7f4f60161751d8a9dab, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑自带线大疆, valueTrans=Cool black comes with cable dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01VJZX8l1ZjOL5tTE8x_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=64.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=64)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=89.0, jxhyPrice=null, skuId=*************, specId=e221939615a36d4e50e779c3466acc35, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑自带线大疆, valueTrans=Cool black comes with cable dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01VJZX8l1ZjOL5tTE8x_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=89.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=89)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=101.0, jxhyPrice=null, skuId=*************, specId=244b61cd0efc60ca0270e8dd2d27e22e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑自带线大疆, valueTrans=Cool black comes with cable dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01VJZX8l1ZjOL5tTE8x_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=101.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=101)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=64.0, jxhyPrice=null, skuId=*************, specId=b6b50db948f83637f0a4659e2f64b03a, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红自带线大疆, valueTrans=China red comes with line dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01URFeYi1ZjOL87IOoK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=64.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=64)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=89.0, jxhyPrice=null, skuId=*************, specId=ceaab80e143e4eaf77066059138178bf, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红自带线大疆, valueTrans=China red comes with line dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01URFeYi1ZjOL87IOoK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=89.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=89)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=101.0, jxhyPrice=null, skuId=*************, specId=3863d63be4c510ec0b87c62bfa62f8c8, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红自带线大疆, valueTrans=China red comes with line dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01URFeYi1ZjOL87IOoK_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=101.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=101)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=61.0, jxhyPrice=null, skuId=*************, specId=3b50ff0f0abace0dae1990172d4355e4, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大疆, valueTrans=Cool black dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jUByva1ZjOL87J8dt_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=61.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=61)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=86.0, jxhyPrice=null, skuId=*************, specId=fc14c85e3a572136d1b7f6cd04b8b733, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大疆, valueTrans=Cool black dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jUByva1ZjOL87J8dt_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=86.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=86)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=98.0, jxhyPrice=null, skuId=*************, specId=db6e893831c5fcf2694d90bac0c16065, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=酷炫黑大疆, valueTrans=Cool black dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01jUByva1ZjOL87J8dt_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=98.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=98)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=61.0, jxhyPrice=null, skuId=*************, specId=dadb44133d308407c627a2fcb9c224e8, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红大疆, valueTrans=China red dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01fGte5T1ZjOL6QrX89_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=50000mAh, valueTrans=50000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=61.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=61)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=86.0, jxhyPrice=null, skuId=*************, specId=e5c9cc68844c28a05dcf855d4b9ccae3, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红大疆, valueTrans=China red dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01fGte5T1ZjOL6QrX89_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=80000mAh, valueTrans=80000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=86.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=86)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=9999, price=98.0, jxhyPrice=null, skuId=*************, specId=fc2c5d6383ac208c5d0b1839efcddf2c, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=3216, attributeName=颜色, attributeNameTrans=Color, value=中国红大疆, valueTrans=China red dji, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01fGte5T1ZjOL6QrX89_!!*************-0-cib.jpg), GoodsDetailResponse.SkuAttribute(attributeId=********, attributeName=电源容量, attributeNameTrans=Power supply capacity, value=100000mAh, valueTrans=100000mAh, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=98.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=98))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=299968, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=1, price=51.0, promotionPrice=null)], quoteType=1, unitInfo=GoodsDetailResponse.UnitInfo(unit=台, transUnit=unit), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=1, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=广东省深圳市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=null, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=f96c473203e61d503836077ba32a0a82, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=bac887cb029926ee5fa29735f13edde4, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=2789a1a5b53bcce942836790ed02e4c9, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=c8bb3edc5c17339a4902dbac3a418071, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=b8b4c9731506e53b0a0825f894c72657, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=754a5a8384ce95f3e4127d436c020c9d, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=b8b292ae2cb12550a0b12384ce427531, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=fa7fb996764fe5d28b31237666184e57, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=1dbfc2b0ab92e4969b50d1560f53b706, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=2654f0b508b5c7f40177b9c2e0ba91bb, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=8164fd83752c912437c4cd1b69f346fa, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=0e008c554633f27ac33cb7ff23285b35, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=e3d4a160c75795bd6768db50b38eae96, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=534c5cfd6b0abd38a963103e2f42ab1f, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=9749ce1b6610fc990c713f9bc5baaf6e, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=a363520837f5f720b54c7379a23c3fc7, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=c1f95bacfbcf5a3de52dd0bba096713d, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=9f5358fbfcf8c8a2190f1cf95c1d2715, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=d14451afb4eaf7f4f60161751d8a9dab, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=e221939615a36d4e50e779c3466acc35, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=244b61cd0efc60ca0270e8dd2d27e22e, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=b6b50db948f83637f0a4659e2f64b03a, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=ceaab80e143e4eaf77066059138178bf, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=3863d63be4c510ec0b87c62bfa62f8c8, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=3b50ff0f0abace0dae1990172d4355e4, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=fc14c85e3a572136d1b7f6cd04b8b733, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=db6e893831c5fcf2694d90bac0c16065, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=dadb44133d308407c627a2fcb9c224e8, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=e5c9cc68844c28a05dcf855d4b9ccae3, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=fc2c5d6383ac208c5d0b1839efcddf2c, skuId=*************, width=8.0, length=16.5, height=4.0, weight=600)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=8.0, length=16.5, height=4.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBBStGyLbGutsWLA-8BjmkZ9g, minOrderQuantity=1, batchNumber=null, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=false), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=true), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=GoodsDetailResponse.SellerMixSetting(generalHunpi=true, mixAmount=400, mixNumber=2), productCargoNumber=MK-653, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=1, compositeServiceScore=3.5, logisticsExperienceScore=3.0, disputeComplaintScore=2.0, offerExperienceScore=0.0, consultingExperienceScore=2.0, repeatPurchasePercent=0.17149117144856985, afterSalesExperienceScore=2.2, collect30DayWithin48HPercent=1.0, qualityRefundWithin30Day=0.027777777777777776), soldOut=139, channelPrice=null, promotionModel=null, tradeScore=5.0, topCategoryId=7, secondCategoryId=50911, thirdCategoryId=1046714, sellingPoint=null, offerIdentities=[tp_member], createDate=2025-04-09 11:03:11, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.d.c.product.PdcProductDetailConvertMapping - [1094191562290552832]:[0] 使用预设ID: *************** 对应平台商品ID: ************
2025-07-07 09:26:38 INFO  [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - [1094191470166859777]:[0] 开始解密旺旺昵称, openUid: BBBStGyLbGutsWLA-8BjmkZ9g
2025-07-07 09:26:38 INFO  [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1094191470166859777]:[0] 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1751851598170access_tokenb49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2openUidBBBStGyLbGutsWLA-8BjmkZ9g 签名: CEE43D68E14FEABF857C45DFE495057A682F6595
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.common.util.MetaInfoHashUtils - [1094191562290552832]:[0] 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=512848be975308038dce9b410609cfa1, metaInfo长度=34822
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191562290552832]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] com.fulfillmen.shop.config.TenantConfig - [1094191562290552832]:[0] 忽略表的多租户处理: pdc_product_mapping
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191562290552832]:[0] 商品详情同步到数据库完成, id: ***************
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191562290552832]:[0] 商品详情更新到缓存完成, id: ***************
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191562290552832]:[0] 自动同步功能已关闭，跳过事件发布
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191562290552832]:[0] 从PdcProductMapping同步产品数据: platformProductId=***************
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191562290552832]:[0] 开始同步产品数据，platformProductId: ***************
2025-07-07 09:26:38 DEBUG [ForkJoinPool.commonPool-worker-2] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191470166859777]:[0] 从缓存获取商品详情, id: ***************
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191562290552832]:[0] SPU创建成功，spuId: 715359839378965
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1094191562290552832]:[0] 多规格SKU创建成功，数量: 30
2025-07-07 09:26:38 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191562290552832]:[0] [GET] /api/products/*************** 200 774ms
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191562290552832]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191562290552832]:[0] 清理增强租户上下文
2025-07-07 09:26:38 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191562290552832]:[0] 过滤器清理租户上下文完成
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191755585052673]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1094191755585052672]:[0] 从缓存中获取租户ID: 10000
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191755585052673]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191755585052672]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191755585052673]:[0] 租户缓存命中: 10000
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1094191755585052672]:[0] 租户缓存命中: 10000
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052672]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191755585052672]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052672]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052673]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191755585052673]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052673]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /api/home/<USER>
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191755585052672]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191755585052672]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191755585052673]:[0] [RegexCors] 正则规则 http://localhost:[0-9]+ 匹配来源: http://localhost:5173
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [1094191755585052673]:[0] [RegexCors] 正则匹配成功: http://localhost:5173
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191755585052672]:[0] auth check result: true
2025-07-07 09:27:23 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191755585052672]:[0] [GET] /api/home/<USER>
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.config.satoken.SaExtensionInterceptor - [1094191755585052673]:[0] auth check result: true
2025-07-07 09:27:23 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191755585052673]:[0] [GET] /api/home/<USER>
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191755585052672]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=4/100
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1094191755585052673]:[0] 全局限流检查通过: key=rate_limit:ip:0:0:0:0:0:0:0:1, current=5/100
2025-07-07 09:27:23 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191755585052672]:[0] 加载热门商品列表（带缓存和同步入库）
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052672]:[0] 开始获取推荐商品数据，语言: EN, 页码: 1, 每页大小: 20
2025-07-07 09:27:23 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191755585052673]:[0] 搜索电子产品（带缓存和同步入库）
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052672]:[0] 推荐商品缓存获取开始，语言: EN, 页码: 1, 强制刷新: false
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052673]:[0] 命中完整缓存, key: search:65d43bf503e3410204fa882720c56e62
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052673]:[0] 缓存统计 - Key: search:65d43bf503e3410204fa882720c56e62, Hit: true
2025-07-07 09:27:23 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191755585052673]:[0] 成功搜索电子产品，数量: 10
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052672]:[0] 命中推荐商品缓存, key: recommend:EN:1:20
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1094191755585052672]:[0] 缓存统计 - Key: recommend:EN:1:20, Hit: true
2025-07-07 09:27:23 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.shop.frontend.service.impl.HomeServiceImpl - [1094191755585052672]:[0] 成功加载热门商品，数量: 20，ID同步状态：正常
2025-07-07 09:27:23 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191755585052673]:[0] [GET] /api/home/<USER>
2025-07-07 09:27:23 INFO  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.f.starter.log.interceptor.handler.LogInterceptor - [1094191755585052672]:[0] [GET] /api/home/<USER>
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191755585052673]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] c.fulfillmen.shop.common.context.UserContextHolder - [1094191755585052672]:[0] 清除用户上下文和 MDC 信息
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191755585052673]:[0] 清理增强租户上下文
2025-07-07 09:27:23 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052673]:[0] 过滤器清理租户上下文完成
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1094191755585052672]:[0] 清理增强租户上下文
2025-07-07 09:27:23 DEBUG [XNIO-1 task-3] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1094191755585052672]:[0] 过滤器清理租户上下文完成
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751851649732}] instanceId:[InstanceId{instanceId=**************:81990, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:27:29 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 09:27:31 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:27:31 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 09:27:31 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 09:27:31 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 09:27:33 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 09:27:34 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 83627 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-07 09:27:34 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-07 09:27:34 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-07 09:27:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 09:27:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 09:27:35 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-07-07 09:27:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-07 09:27:35 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:27:35 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:27:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-07 09:27:35 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-07 09:27:35 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-07 09:27:36 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2277 ms
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-07 09:27:36 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-07 09:27:36 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-07 09:27:36 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 09:27:36 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-07 09:27:36 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 09:27:37 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-07 09:27:37 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [MANUAL, AUTO, DISABLED]
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 09:27:37 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-07 09:27:37 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6482eef
2025-07-07 09:27:38 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:83627, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1751851658177}] - instanceId:[InstanceId{instanceId=**************:83627, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 09:27:38 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 09:27:38 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 54 ms
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 5.56 seconds (process running for 6.241)
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-07 09:27:39 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-07 09:27:39 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-USD = 0.1396
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-EUR = 0.1185
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-JPY = 20.1073
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-KRW = 190.1975
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.service.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-07-07 09:27:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-07 09:27:39 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功: CNY-INR = 11.9314
2025-07-07 09:27:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-07 09:27:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-07 09:27:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-07 09:27:40 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-07 09:27:40 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-07 09:27:40 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-07 09:27:40 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 09:27:40 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 09:27:40 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-07 09:27:40 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-07 09:27:40 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5e1e0d7e
2025-07-07 09:27:40 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 09:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 09:27:37,235 to 2025-07-07 09:30:00,006
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.07|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.07| 20.00%|            10|             2|             0|             0|        0.0|          0
currency.rate._remote      |      0.06|100.00%|             8|             8|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 09:33:19 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 09:33:19 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1751851999745}] instanceId:[InstanceId{instanceId=**************:83627, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 09:33:19 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 09:57:11 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 09:57:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 09:57:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 09:57:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-07-07 09:57:12 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:57:12 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 09:57:13 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 09:57:13 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:57:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2192 ms
2025-07-07 09:57:13 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 09:57:13 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 09:57:13 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 09:57:14 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 09:57:14 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 09:57:14 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2fc2a205
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:10298, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751853435405}] - instanceId:[InstanceId{instanceId=**************:10298, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 09:57:15 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 50 ms
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 09:57:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 09:57:18 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 09:57:18 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 09:57:18 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@5f0947e7
2025-07-07 09:57:18 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 09:57:14,469 to 2025-07-07 10:00:00,011
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.08|  0.00%|             1|             0|             0|             0|      194.0|        194
alibaba:category:list:_local |      0.08|  0.00%|             1|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.08|  0.00%|             1|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.06|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.06|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.06|  0.00%|            10|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 10:00:00 WARN  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094199961724444672]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:14:59 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 10:00:00,011 to 2025-07-07 10:14:59,993
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00| 50.00%|             4|             2|             0|             0|        0.0|          0
pdc:product:_local           |      0.00| 50.00%|             4|             2|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             2|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:15:12 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751854512958}] instanceId:[InstanceId{instanceId=**************:10298, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:15:12 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:15:15 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:15:15 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:15:15 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:15:15 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:15:17 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:15:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:15:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:15:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-07-07 10:15:18 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:15:18 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:15:19 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:15:19 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:15:19 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2341 ms
2025-07-07 10:15:19 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:15:19 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:15:19 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:15:20 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:15:20 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:15:20 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7a22a3c2
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:26541, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751854521437}] - instanceId:[InstanceId{instanceId=**************:26541, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:15:21 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 59 ms
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:15:22 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:15:23 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:15:23 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:15:23 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@4f84b7f9
2025-07-07 10:15:23 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:16:34 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094204130761957376]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:21:59 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751854919573}] instanceId:[InstanceId{instanceId=**************:26541, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:21:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:22:01 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:22:01 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:22:01 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:22:01 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:22:03 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:22:04 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:22:04 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:22:05 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-07 10:22:05 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:22:05 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:22:06 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:22:06 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:22:06 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2391 ms
2025-07-07 10:22:06 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:22:06 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:22:06 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:22:07 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:22:07 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:22:07 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1cb37ee4
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:32714, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751854928600}] - instanceId:[InstanceId{instanceId=**************:32714, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:22:08 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 56 ms
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:22:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:22:10 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:22:10 INFO  [RMI TCP Connection(6)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:22:11 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@74b0c37d
2025-07-07 10:22:11 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:22:20 WARN  [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:27:02 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:27:02 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751855222885}] instanceId:[InstanceId{instanceId=**************:32714, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:27:02 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:27:04 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:27:04 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:27:04 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:27:04 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:27:06 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:27:07 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:27:07 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:27:07 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-07 10:27:08 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:27:08 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:27:09 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:27:09 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:27:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2352 ms
2025-07-07 10:27:09 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:27:09 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:27:09 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:27:10 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:27:10 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:27:10 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:27:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@62ce72ff
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:37172, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751855231327}] - instanceId:[InstanceId{instanceId=**************:37172, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:27:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 70 ms
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:27:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:27:13 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:27:13 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:27:13 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@61fd8821
2025-07-07 10:27:13 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:27:44 WARN  [XNIO-1 task-3] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094206942354923520]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 10:27:10,340 to 2025-07-07 10:30:00,002
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.06|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.06|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.06|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.02| 66.67%|             3|             2|             0|             0|        0.0|          0
pdc:product:_local         |      0.02| 33.33%|             3|             1|             0|             0|        0.0|          0
pdc:product:_remote        |      0.01| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:search:        |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.02|  0.00%|             3|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:31:53 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751855513128}] instanceId:[InstanceId{instanceId=**************:37172, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:31:53 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:31:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:31:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:31:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:31:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:31:57 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:31:58 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:31:58 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:31:58 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-07 10:31:58 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:31:58 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:31:59 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:31:59 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:31:59 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2253 ms
2025-07-07 10:32:00 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:32:00 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:32:00 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:32:00 INFO  [redisson-netty-1-6] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:32:00 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:32:00 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2881ad47
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:41475, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751855521709}] - instanceId:[InstanceId{instanceId=**************:41475, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:32:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 57 ms
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:32:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:32:04 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:32:04 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:32:04 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@54433db8
2025-07-07 10:32:04 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:43:10 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094210827895234560]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 10:32:00,628 to 2025-07-07 10:45:00,036
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.01|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.01|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.01|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00| 66.67%|             3|             2|             0|             0|        0.0|          0
pdc:product:_local         |      0.00| 33.33%|             3|             1|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 10:48:07 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:48:07 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751856487852}] instanceId:[InstanceId{instanceId=**************:41475, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:48:07 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:48:09 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:48:09 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:48:09 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:48:09 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:48:12 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:48:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:48:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:48:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-07 10:48:13 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:48:13 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:48:14 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:48:14 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:48:14 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2306 ms
2025-07-07 10:48:14 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:48:14 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:48:15 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:48:15 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:48:15 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:48:15 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1fcf9739
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:55394, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751856496494}] - instanceId:[InstanceId{instanceId=**************:55394, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:48:16 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 56 ms
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:48:17 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:48:18 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:48:18 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:48:18 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@7353c7d9
2025-07-07 10:48:18 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:48:27 WARN  [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 10:49:32 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 10:49:32 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751856572369}] instanceId:[InstanceId{instanceId=**************:55394, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 10:49:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 10:49:34 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 10:49:34 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 10:49:34 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 10:49:34 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 10:49:36 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 10:49:37 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 10:49:37 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 10:49:37 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-07-07 10:49:38 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:49:38 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 10:49:38 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 10:49:38 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:49:38 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2233 ms
2025-07-07 10:49:39 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 10:49:39 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 10:49:39 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 10:49:39 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 10:49:39 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 10:49:39 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1cb37ee4
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:56693, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751856580771}] - instanceId:[InstanceId{instanceId=**************:56693, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 10:49:40 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 58 ms
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 10:49:41 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 10:49:43 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 10:49:43 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 10:49:43 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@416c104c
2025-07-07 10:49:43 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 10:49:50 WARN  [ForkJoinPool.commonPool-worker-1] [tid::uId::ip::os::browser:] io.micrometer.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 11:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 10:49:39,770 to 2025-07-07 11:00:00,010
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.02|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.02| 10.00%|            10|             1|             0|             0|        0.0|          0
currency.rate._remote      |      0.01|100.00%|             9|             9|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.01| 66.67%|             6|             4|             0|             0|        0.0|          0
pdc:product:_local         |      0.01| 66.67%|             6|             4|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             2|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.01|100.00%|             6|             6|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.01| 66.67%|             6|             4|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 11:08:55 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751857735797}] instanceId:[InstanceId{instanceId=**************:56693, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 11:08:55 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 11:08:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 11:08:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 11:08:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 11:08:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 11:19:12 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 11:19:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 11:19:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 11:19:13 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-07-07 11:19:14 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 11:19:14 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 11:19:15 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 11:19:15 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 11:19:15 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2349 ms
2025-07-07 11:19:15 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 11:19:15 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 11:19:15 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 11:19:16 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 11:19:16 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 11:19:16 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 11:19:16 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6482eef
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:84649, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=0, lastTimeStamp=1751858357279}] - instanceId:[InstanceId{instanceId=**************:84649, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 11:19:17 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 63 ms
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 11:19:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 11:19:19 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 11:19:19 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 11:19:19 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@dc0da05
2025-07-07 11:19:19 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 11:20:51 WARN  [XNIO-1 task-3] [tid:10000:uId:1919943296628277250:ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094220309220089856]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 11:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 11:19:16,292 to 2025-07-07 11:30:00,008
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
cart:                        |      0.01| 88.89%|             9|             8|             0|             0|       11.0|         11
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.02|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local         |      0.02|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.02|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00| 66.67%|             3|             2|             0|             0|        0.0|          0
pdc:product:_local           |      0.00| 66.67%|             3|             2|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.02| 72.73%|            11|             8|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.02| 54.55%|            11|             6|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.01| 40.00%|             5|             2|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 11:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 11:30:00,008 to 2025-07-07 11:45:00,016
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 11:59:59 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 11:45:00,016 to 2025-07-07 11:59:59,993
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 12:14:59 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 11:59:59,993 to 2025-07-07 12:14:59,996
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 12:29:59 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 12:14:59,996 to 2025-07-07 12:29:59,997
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 12:44:59 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 12:29:59,997 to 2025-07-07 12:44:59,995
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 13:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 12:44:59,995 to 2025-07-07 13:00:00,001
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 13:15:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 13:00:00,001 to 2025-07-07 13:15:00,036
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 13:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 13:15:00,036 to 2025-07-07 13:30:00,046
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|      174.0|        174
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 13:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 13:30:00,046 to 2025-07-07 13:45:00,050
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 14:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 13:45:00,050 to 2025-07-07 14:00:00,052
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 14:15:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 14:00:00,052 to 2025-07-07 14:15:00,059
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 14:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 14:15:00,059 to 2025-07-07 14:30:00,066
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 14:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 14:30:00,066 to 2025-07-07 14:45:00,040
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 15:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 14:45:00,040 to 2025-07-07 15:00:00,041
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 15:15:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 15:00:00,041 to 2025-07-07 15:15:00,067
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 15:30:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 15:15:00,067 to 2025-07-07 15:30:00,066
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|      170.0|        170
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 15:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 15:30:00,066 to 2025-07-07 15:45:00,063
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 15:48:07 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 15:48:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 15:48:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 15:48:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-07-07 15:48:09 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 15:48:09 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 15:48:09 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 15:48:09 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 15:48:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2311 ms
2025-07-07 15:48:10 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 15:48:10 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 15:48:10 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 15:48:10 INFO  [redisson-netty-1-6] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 15:48:10 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 15:48:10 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 15:48:11 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 15:48:11 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2fc2a205
2025-07-07 15:48:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:19667, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 15:48:11 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=0, lastTimeStamp=1751874491947}] - instanceId:[InstanceId{instanceId=**************:19667, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 94 ms
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 15:48:12 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 15:48:14 INFO  [RMI TCP Connection(1)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 15:48:14 INFO  [RMI TCP Connection(3)-*************] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 15:48:14 INFO  [RMI TCP Connection(1)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@756376c2
2025-07-07 15:48:14 INFO  [RMI TCP Connection(1)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 15:55:24 WARN  [XNIO-1 task-6] [tid:10000:uId:1919943296628277250:ip:127.0.0.1:os:OSX:browser:Chrome *********] io.micrometer.core.instrument.MeterRegistry - [1094289401901408256]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-07-07 16:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 15:48:10,933 to 2025-07-07 16:00:00,008
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
cart:                        |      0.00|  0.00%|             1|             0|             0|             0|      173.0|        173
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.01|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local         |      0.01|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.01|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:_local           |      0.00| 50.00%|             2|             1|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|  0.00%|             3|             0|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 16:15:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-07 16:00:00,008 to 2025-07-07 16:14:59,999
cache                        |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
alibaba:category:list:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
alibaba:category:list:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
cart:                        |      0.00|100.00%|             1|             1|             0|             0|        0.0|          0
categories.                  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local            |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local   |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:                 |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local           |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:          |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
pdc:product:search:_local    |      0.00|  0.00%|             2|             0|             0|             0|        0.0|          0
pdc:product:search:_remote   |      0.00|100.00%|             2|             2|             0|             0|        0.0|          0
-----------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-07 16:20:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 16:20:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=0, lastTimeStamp=1751876421731}] instanceId:[InstanceId{instanceId=**************:19667, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 16:20:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 16:20:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:20:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 16:20:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:20:23 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-07 16:20:26 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-07 16:20:27 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-07 16:20:27 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-07 16:20:27 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-07-07 16:20:28 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 16:20:28 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-07 16:20:28 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-07 16:20:28 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 16:20:28 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2290 ms
2025-07-07 16:20:29 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-07 16:20:29 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-07 16:20:29 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-07 16:20:29 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-07 16:20:29 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-07 16:20:29 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-07 16:20:30 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-07 16:20:30 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@58a63629
2025-07-07 16:20:30 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:47672, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 16:20:30 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1751876430949}] - instanceId:[InstanceId{instanceId=**************:47672, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 54 ms
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-07 16:20:31 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-07 16:20:33 INFO  [RMI TCP Connection(6)-*************] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-07 16:20:33 INFO  [RMI TCP Connection(4)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-07 16:20:33 INFO  [RMI TCP Connection(4)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@3f9a133e
2025-07-07 16:20:33 INFO  [RMI TCP Connection(4)-*************] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-07 16:21:59 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/order
jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Name is null
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Name is null
	at java.base/java.lang.Enum.valueOf(Enum.java:291)
	at com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.valueOf(TzOrderPurchaseStatusEnum.java:52)
	at com.fulfillmen.shop.frontend.service.impl.FrontendOrderServiceImpl.getOrderList(FrontendOrderServiceImpl.java:444)
	at com.fulfillmen.shop.frontend.controller.OrderController.getOrderList(OrderController.java:109)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 81 common frames omitted
2025-07-07 16:21:59 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-07 16:23:50 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/order
jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Name is null
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Name is null
	at java.base/java.lang.Enum.valueOf(Enum.java:291)
	at com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.valueOf(TzOrderPurchaseStatusEnum.java:52)
	at com.fulfillmen.shop.frontend.service.impl.FrontendOrderServiceImpl.getOrderList(FrontendOrderServiceImpl.java:444)
	at com.fulfillmen.shop.frontend.controller.OrderController.getOrderList(OrderController.java:109)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 81 common frames omitted
2025-07-07 16:23:50 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-07 16:25:06 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/order
jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Name is null
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Name is null
	at java.base/java.lang.Enum.valueOf(Enum.java:291)
	at com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.valueOf(TzOrderPurchaseStatusEnum.java:52)
	at com.fulfillmen.shop.frontend.service.impl.FrontendOrderServiceImpl.getOrderList(FrontendOrderServiceImpl.java:444)
	at com.fulfillmen.shop.frontend.controller.OrderController.getOrderList(OrderController.java:109)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 81 common frames omitted
2025-07-07 16:25:06 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-07 16:25:47 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/order
jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Name is null
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Name is null
	at java.base/java.lang.Enum.valueOf(Enum.java:291)
	at com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.valueOf(TzOrderPurchaseStatusEnum.java:52)
	at com.fulfillmen.shop.frontend.service.impl.FrontendOrderServiceImpl.getOrderList(FrontendOrderServiceImpl.java:444)
	at com.fulfillmen.shop.frontend.controller.OrderController.getOrderList(OrderController.java:109)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 81 common frames omitted
2025-07-07 16:25:47 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-07 16:27:42 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/order
jakarta.servlet.ServletException: Request processing failed: java.lang.NullPointerException: Name is null
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Name is null
	at java.base/java.lang.Enum.valueOf(Enum.java:291)
	at com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum.valueOf(TzOrderPurchaseStatusEnum.java:52)
	at com.fulfillmen.shop.frontend.service.impl.FrontendOrderServiceImpl.getOrderList(FrontendOrderServiceImpl.java:443)
	at com.fulfillmen.shop.frontend.controller.OrderController.getOrderList(OrderController.java:109)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 81 common frames omitted
2025-07-07 16:27:42 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$saInterceptor$1(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 16:27:42 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-07 16:27:42 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1751876862123}] instanceId:[InstanceId{instanceId=**************:47672, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-07 16:27:42 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
