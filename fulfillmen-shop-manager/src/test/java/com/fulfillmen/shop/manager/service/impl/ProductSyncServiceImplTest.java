/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCategoryMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSaleInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * ProductSyncServiceImpl 单元测试
 *
 * <AUTHOR>
 * @date 2025/6/16 10:00
 * @description: 测试产品同步服务逻辑
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ProductSyncServiceImpl 单元测试")
class ProductSyncServiceImplTest {

    @Mock
    private PdcProductMappingRepository pdcProductMappingRepository;

    @Mock
    private TzProductSpuMapper tzProductSpuMapper;

    @Mock
    private TzProductSkuMapper tzProductSkuMapper;

    @Mock
    private SysAlibabaCategoryMapper sysAlibabaCategoryMapper;

    @Mock
    private TzProductMapping tzProductMapping;

    @InjectMocks
    private ProductSyncServiceImpl productSyncService;

    private Long testTenantId;
    private String testPlatformProductId;
    private TzProductSpu testSpu;
    private TzProductSku testSku;
    private AlibabaProductDetailDTO testProductDetail;

    @BeforeEach
    void setUp() {
        testTenantId = 1001L;
        testPlatformProductId = "123456789";

        // 创建测试SPU
        testSpu = createTestSpu();

        // 创建测试SKU
        testSku = createTestSku();

        // 创建测试产品详情
        testProductDetail = createTestProductDetail();
    }

    @Test
    @DisplayName("测试同步产品 - SPU不存在时创建新产品")
    void testSyncProductByPlatformId_CreateNew() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟SPU不存在
            when(tzProductSpuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // 模拟获取产品详情
            when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
                .thenReturn(testProductDetail);

            // 模拟映射转换
            when(tzProductMapping.toTzProductSpu(testProductDetail)).thenReturn(testSpu);
            when(tzProductMapping.toTzProductSkuList(anyList(), eq(testSpu.getId()), eq(testPlatformProductId),testSpu.getMinOrderQuantity()))
                .thenReturn(List.of(testSku));

            // 模拟转换为DTO
            TzProductDTO expectedDTO = createTestProductDTO();
            when(tzProductMapping.toTzProductDTO(any(TzProductSpu.class), anyList(), any())).thenReturn(expectedDTO);

            // 模拟数据库操作
            when(tzProductSpuMapper.insert(testSpu)).thenReturn(1);
            when(tzProductSkuMapper.insertBatch(anyList())).thenReturn(true);

            // 执行测试
            TzProductDTO result = productSyncService.syncProductByPlatformId(testPlatformProductId);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedDTO.getId(), result.getId());
            assertEquals(expectedDTO.getTitle(), result.getTitle());

            // 验证方法调用
            verify(tzProductSpuMapper).insert(testSpu);
            verify(tzProductSkuMapper).insertBatch(anyList());
            verify(tzProductMapping).toTzProductSpu(testProductDetail);
            verify(tzProductMapping).toTzProductSkuList(anyList(), eq(testSpu.getId()), eq(testPlatformProductId),testSpu.getMinOrderQuantity());
            verify(tzProductMapping).toTzProductDTO(any(TzProductSpu.class), anyList(), any());
        }
    }

    @Test
    @DisplayName("测试同步产品 - SPU已存在时直接返回")
    void testSyncProductByPlatformId_ExistingSpu() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟SPU已存在
            when(tzProductSpuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testSpu);

            // 模拟获取SKU列表
            when(tzProductSkuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(List.of(testSku));

            // 模拟转换为DTO
            TzProductDTO expectedDTO = createTestProductDTO();
            when(tzProductMapping.toTzProductDTO(any(TzProductSpu.class), anyList(), any())).thenReturn(expectedDTO);

            // 执行测试
            TzProductDTO result = productSyncService.syncProductByPlatformId(testPlatformProductId);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedDTO.getId(), result.getId());

            // 验证不会调用创建方法
            verify(tzProductSpuMapper, never()).insert((TzProductSpu) any());
            verify(tzProductSkuMapper, never()).insertBatch(anyList());
            verify(pdcProductMappingRepository, never()).getProductDetailWithCache(anyLong(), anyBoolean());

            // 验证会调用转换方法
            verify(tzProductMapping).toTzProductDTO(any(TzProductSpu.class), anyList(), any());
        }
    }

    @Test
    @DisplayName("测试同步单品产品")
    void testSyncProductByPlatformId_SingleItem() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 创建单品测试数据
            testProductDetail.setSingleItem(true);
            testSpu.setIsSingleItem(TzProductSpuSingleItemEnum.YES);

            // 模拟SPU不存在
            when(tzProductSpuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // 模拟获取产品详情
            when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
                .thenReturn(testProductDetail);

            // 模拟映射转换
            when(tzProductMapping.toTzProductSpu(testProductDetail)).thenReturn(testSpu);
            when(tzProductMapping.toSingleItemSku(testProductDetail, testSpu.getId())).thenReturn(testSku);

            // 模拟转换为DTO
            TzProductDTO expectedDTO = createTestProductDTO();
            when(tzProductMapping.toTzProductDTO(any(TzProductSpu.class), anyList(), any())).thenReturn(expectedDTO);

            // 模拟数据库操作
            when(tzProductSpuMapper.insert(testSpu)).thenReturn(1);
            when(tzProductSkuMapper.insert((TzProductSku) any())).thenReturn(1);

            // 执行测试
            TzProductDTO result = productSyncService.syncProductByPlatformId(testPlatformProductId);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedDTO.getId(), result.getId());

            // 验证方法调用
            verify(tzProductSpuMapper).insert(testSpu);
            verify(tzProductSkuMapper).insert((TzProductSku) any());
            verify(tzProductMapping).toSingleItemSku(testProductDetail, testSpu.getId());
            verify(tzProductMapping).toTzProductDTO(any(TzProductSpu.class), anyList(), any());
        }
    }

    @Test
    @DisplayName("测试获取SKU列表")
    void testGetSkuListBySpuId() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟数据库查询
            List<TzProductSku> expectedSkuList = List.of(testSku);
            when(tzProductSkuMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(expectedSkuList);

            // 执行测试
            List<TzProductSku> result = productSyncService.getSkuListBySpuId(testSpu.getId());

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testSku.getId(), result.get(0).getId());

            // 验证查询条件
            verify(tzProductSkuMapper).selectList(argThat(wrapper -> {
                // 这里可以验证查询条件是否正确
                return true;
            }));
        }
    }

    @Test
    @DisplayName("测试检查是否为单品")
    void testIsSingleItem() {
        // 模拟获取产品详情
        when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
            .thenReturn(testProductDetail);

        // 测试多规格商品
        testProductDetail.setSingleItem(false);
        boolean result = productSyncService.isSingleItem(testPlatformProductId);
        assertFalse(result);

        // 测试单品
        testProductDetail.setSingleItem(true);
        result = productSyncService.isSingleItem(testPlatformProductId);
        assertTrue(result);
    }

    @Test
    @DisplayName("测试根据平台ID获取SKU")
    void testGetSkuByPlatformIds() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            String platformSkuId = "sku123";

            // 模拟数据库查询
            when(tzProductSkuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testSku);

            // 执行测试
            TzProductSku result = productSyncService.getSkuByPlatformIds(testPlatformProductId, platformSkuId);

            // 验证结果
            assertNotNull(result);
            assertEquals(testSku.getId(), result.getId());

            // 验证查询条件
            verify(tzProductSkuMapper).selectOne(argThat(wrapper -> {
                // 这里可以验证查询条件是否正确
                return true;
            }));
        }
    }

    @Test
    @DisplayName("测试获取单品价格")
    void testGetSingleItemPrice() {
        // 模拟获取产品详情
        when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
            .thenReturn(testProductDetail);

        // 模拟价格提取
        BigDecimal expectedPrice = new BigDecimal("99.99");
        when(tzProductMapping.extractDropShippingPriceFromProductDetail(testProductDetail))
            .thenReturn(expectedPrice);

        // 执行测试
        BigDecimal result = productSyncService.getSingleItemPrice(testPlatformProductId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedPrice, result);

        // 验证方法调用
        verify(tzProductMapping).extractDropShippingPriceFromProductDetail(testProductDetail);
    }

    @Test
    @DisplayName("测试获取单品默认SKU")
    void testGetSingleItemDefaultSku() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟数据库查询
            when(tzProductSkuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testSku);

            // 执行测试
            TzProductSku result = productSyncService.getSingleItemDefaultSku(testSpu.getId());

            // 验证结果
            assertNotNull(result);
            assertEquals(testSku.getId(), result.getId());

            // 验证查询条件包含LIMIT 1
            verify(tzProductSkuMapper).selectOne(argThat(wrapper -> {
                // 这里可以验证查询条件是否正确
                return true;
            }));
        }
    }

    @Test
    @DisplayName("测试强制创建单品默认SKU")
    void testForceCreateDefaultSkuForSingleItem() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟SKU不存在
            when(tzProductSkuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // 模拟SPU存在
            when(tzProductSpuMapper.selectById(testSpu.getId())).thenReturn(testSpu);

            // 模拟获取产品详情
            when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
                .thenReturn(testProductDetail);

            // 模拟映射转换
            when(tzProductMapping.toSingleItemSku(testProductDetail, testSpu.getId())).thenReturn(testSku);

            // 模拟数据库操作 - 明确指定参数类型以解决方法调用歧义
            when(tzProductSkuMapper.insert((TzProductSku) any())).thenReturn(1);

            // 执行测试
            TzProductSku result = productSyncService.forceCreateDefaultSkuForSingleItem(testSpu
                .getId(), testPlatformProductId);

            // 验证结果
            assertNotNull(result);
            assertEquals(testSku.getId(), result.getId());

            // 验证方法调用
            verify(tzProductSkuMapper).insert((TzProductSku) any());
            verify(tzProductMapping).toSingleItemSku(testProductDetail, testSpu.getId());
        }
    }

    @Test
    @DisplayName("测试产品详情不存在时的异常处理")
    void testSyncProductByPlatformId_ProductDetailNotFound() {
        // 模拟UserContextHolder
        try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
            mockedUserContext.when(UserContextHolder::getTenantId).thenReturn(testTenantId);

            // 模拟SPU不存在
            when(tzProductSpuMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // 模拟产品详情不存在
            when(pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(testPlatformProductId), false))
                .thenReturn(null);

            // 执行测试
            TzProductDTO result = productSyncService.syncProductByPlatformId(testPlatformProductId);

            // 验证结果
            assertNull(result);

            // 验证不会调用创建方法
            verify(tzProductSpuMapper, never()).insert((TzProductSpu) any());
            verify(tzProductSkuMapper, never()).insertBatch(anyList());
        }
    }

    @Test
    @DisplayName("测试无效平台产品ID的异常处理")
    void testGetSingleItemPrice_InvalidProductId() {
        String invalidProductId = "invalid";

        // 执行测试
        BigDecimal result = productSyncService.getSingleItemPrice(invalidProductId);

        // 验证结果
        assertNull(result);

        // 验证不会调用产品详情获取
        verify(pdcProductMappingRepository, never()).getProductDetailWithCache(anyLong(), anyBoolean());
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的SPU
     */
    private TzProductSpu createTestSpu() {
        TzProductSpu spu = new TzProductSpu();
        spu.setId(2001L);
        spu.setTenantId(testTenantId);
        spu.setPlatformCode(PlatformCodeEnum.PLATFORM_CODE_1688);
        spu.setPdcPlatformProductId(testPlatformProductId);
        spu.setTitle("测试产品标题");
        spu.setTitleTrans("Test Product Title");
        spu.setDescription("测试产品描述");
        spu.setName("测试产品标题");
        spu.setNameTrans("Test Product Title");
        spu.setCategoryId(100L);
        spu.setCategoryName("测试分类");
        spu.setCategoryNameTrans("Test Category");
        spu.setMainImage("http://example.com/main.jpg");
        spu.setWhiteImage("http://example.com/white.jpg");
        spu.setIsSingleItem(TzProductSpuSingleItemEnum.NO);
        spu.setIsDeleted(0L);
        return spu;
    }

    /**
     * 创建测试用的SKU
     */
    private TzProductSku createTestSku() {
        TzProductSku sku = new TzProductSku();
        sku.setId(3001L);
        sku.setSpuId(2001L);
        sku.setTenantId(testTenantId);
        sku.setPlatformCode(PlatformCodeEnum.PLATFORM_CODE_1688);
        sku.setPlatformProductId(testPlatformProductId);
        sku.setPlatformSku("sku123");
        sku.setSku("SKU_123");
        sku.setBarcode("123");
        sku.setImage("http://example.com/sku.jpg");
        sku.setQuantity(100);
        sku.setPrice(new BigDecimal("99.99"));
        sku.setDropShippingPrice(new BigDecimal("89.99"));
        sku.setSpecs(List.of());
        sku.setSalesCount(0);
        sku.setIsDeleted(0L);
        return sku;
    }

    /**
     * 创建测试用的产品详情DTO
     */
    private AlibabaProductDetailDTO createTestProductDetail() {
        AlibabaProductDetailDTO productDetail = new AlibabaProductDetailDTO();
        productDetail.setId(1L);
        productDetail.setPlatformProductId(testPlatformProductId);
        productDetail.setTitle("测试产品标题");
        productDetail.setTitleTrans("Test Product Title");
        productDetail.setDescription("测试产品描述");
        productDetail.setCategoryId(100L);
        productDetail.setCategoryName("测试分类");
        productDetail.setCategoryNameTrans("Test Category");
        productDetail.setWhiteImage("http://example.com/white.jpg");
        productDetail.setSingleItem(false);
        productDetail.setPrice(new BigDecimal("99.99"));

        // 设置图片列表
        List<String> images = new ArrayList<>();
        images.add("http://example.com/main.jpg");
        images.add("http://example.com/image2.jpg");
        productDetail.setImages(images);

        // 设置分销信息
        AlibabaProductSaleInfoDTO.FenxiaoSaleInfo fenxiaoInfo = AlibabaProductSaleInfoDTO.FenxiaoSaleInfo.builder()
            .onePiecePrice(new BigDecimal("89.99"))
            .offerPrice(new BigDecimal("99.99"))
            .build();

        // 设置销售信息
        AlibabaProductSaleInfoDTO saleInfo = AlibabaProductSaleInfoDTO.builder()
            .amountOnSale(50)
            .minOrderQuantity(1)
            .fenxiaoSaleInfo(fenxiaoInfo)
            .build();

        productDetail.setProductSaleInfo(saleInfo);

        // 设置SKU列表（多规格商品）
        List<AlibabaProductSkuDTO> skuList = new ArrayList<>();
        AlibabaProductSkuDTO skuDTO = AlibabaProductSkuDTO.builder()
            .skuId(1001L)
            .price(new BigDecimal("99.99"))
            .offerPrice(new BigDecimal("89.99"))
            .amountOnSale(100)
            .build();
        skuList.add(skuDTO);
        productDetail.setProductSkuList(skuList);

        return productDetail;
    }

    private TzProductDTO createTestProductDTO() {
        TzProductDTO dto = new TzProductDTO();
        dto.setId(2001L);
        dto.setTitle("测试产品标题");
        return dto;
    }
}
