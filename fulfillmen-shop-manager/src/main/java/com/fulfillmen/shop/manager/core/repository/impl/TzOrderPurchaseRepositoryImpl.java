/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzShoppingCartMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import com.fulfillmen.shop.manager.service.OrderStatusSyncService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单仓储实现
 *
 * <pre>
 * 负责订单相关的数据持久化操作，集成状态管理系统：
 * 1. 创建完整的订单数据（采购订单、供应商订单、订单项）
 * 2. 初始化订单状态管理字段
 * 3. 触发状态计算和同步
 * 4. 处理购物车清理
 * 5. 确保数据一致性
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/30 14:50
 * @description 订单仓储层实现，集成状态管理系统
 * @since 1.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TzOrderPurchaseRepositoryImpl extends CrudRepository<TzOrderPurchaseMapper, TzOrderPurchase> implements TzOrderPurchaseRepository {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final TzShoppingCartMapper shoppingCartMapper;
    private final OrderStatusSyncService statusSyncService;

    /**
     * 创建采购订单
     *
     * <pre>
     * 完整的订单创建流程：
     * 1. 初始化并保存采购订单（主订单）
     * 2. 初始化并保存供应商订单（子订单）
     * 3. 初始化并保存订单项（商品明细）
     * 4. 更新状态管理统计字段
     * 5. 触发状态计算和同步
     * 6. 清理购物车数据
     * 7. 记录创建日志
     * </pre>
     *
     * @param orderContext 订单上下文
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPurchaseOrder(OrderContext orderContext) {
        TzOrderPurchase purchaseOrder = orderContext.getPurchaseOrder();
        List<TzOrderSupplier> supplierOrders = orderContext.getSupplierOrders();
        List<TzOrderItem> orderItems = orderContext.getOrderItems();

        log.info("开始创建订单，买家ID: {}, 采购的供应商数量: {} , sku 数量: {}",
                purchaseOrder.getBuyerId(), supplierOrders.size(), orderItems.size());

        try {
            // 1. 初始化并保存采购订单（主订单）
            initializePurchaseOrderStatus(purchaseOrder, supplierOrders);
            this.save(purchaseOrder);
            log.info("采购订单保存成功，订单ID: {}", purchaseOrder.getId());

            // 2. 初始化并保存供应商订单（子订单）
            if (!CollectionUtils.isEmpty(supplierOrders)) {
                for (TzOrderSupplier supplierOrder : supplierOrders) {
                    // 设置采购订单ID关联
                    supplierOrder.setPurchaseOrderId(purchaseOrder.getId());
                    // 初始化供应商订单状态
                    initializeSupplierOrderStatus(supplierOrder, orderItems);
                }
                orderSupplierMapper.insertBatch(supplierOrders);
                log.info("供应商订单保存成功，数量: {}", supplierOrders.size());
            }

            // 3. 初始化并保存订单项（商品明细）
            if (!CollectionUtils.isEmpty(orderItems)) {
                for (TzOrderItem orderItem : orderItems) {
                    // 设置采购订单ID关联
                    orderItem.setPurchaseOrderId(purchaseOrder.getId());
                    // 查找对应的供应商订单ID
                    TzOrderSupplier matchedSupplier = findMatchedSupplierOrder(orderItem, supplierOrders);
                    if (matchedSupplier != null) {
                        orderItem.setSupplierOrderId(matchedSupplier.getId());
                    }
                    // 初始化订单项状态
                    initializeOrderItemStatus(orderItem);
                }
                orderItemMapper.insertBatch(orderItems);
                log.info("订单项保存成功，数量: {}", orderItems.size());
            }

            // 4. 更新状态管理统计字段
            updateStatusManagementFields(purchaseOrder, supplierOrders, orderItems);

            // 5. 触发状态计算和同步
            triggerStatusCalculation(purchaseOrder.getId());

            // 6. 清理购物车数据
            cleanupShoppingCart(orderContext.getShoppingCartIds());

            log.info("订单创建成功，采购订单号: {}, 供应商数量: {}, 商品项数: {}, 总金额: {}",
                    purchaseOrder.getPurchaseOrderNo(), supplierOrders.size(), orderItems.size(),
                    purchaseOrder.getGoodsAmount());

        } catch (BusinessExceptionI18n e) {
            log.error("订单创建业务异常，买家ID: {}, 错误: {}", purchaseOrder.getBuyerId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("订单创建系统异常，买家ID: {}", purchaseOrder.getBuyerId(), e);
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.VALIDATION_FAILED, e.getMessage());
        }
    }

    /**
     * 初始化采购订单状态管理字段
     */
    private void initializePurchaseOrderStatus(TzOrderPurchase purchaseOrder, List<TzOrderSupplier> supplierOrders) {
        // 设置初始状态
        purchaseOrder.setOrderStatus(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED);

        // 初始化状态管理字段
        purchaseOrder.setSupplierCount(supplierOrders != null ? supplierOrders.size() : 0);
        purchaseOrder.setCompletedSupplierCount(0);
        purchaseOrder.setStatusCalculatedAt(LocalDateTime.now());
        purchaseOrder.setStatusVersion(1);

        // 设置时间字段
        LocalDateTime now = LocalDateTime.now();
        purchaseOrder.setOrderDate(now);

        log.debug("采购订单状态初始化完成，供应商数量: {}", purchaseOrder.getSupplierCount());
    }

    /**
     * 初始化供应商订单状态管理字段
     */
    private void initializeSupplierOrderStatus(TzOrderSupplier supplierOrder, List<TzOrderItem> allOrderItems) {
        // 设置初始状态
        supplierOrder.setStatus(TzOrderSupplierStatusEnum.PENDING_PAYMENT);

        // 计算该供应商的订单项数量
        int itemCount = (int) allOrderItems.stream()
                .filter(item -> supplierOrder.getId().equals(item.getSupplierOrderId()))
                .count();

        // 初始化状态管理字段
        supplierOrder.setLineItemCount(itemCount);
        supplierOrder.setCompletedItemCount(0);
        supplierOrder.setStatusUpdatedAt(LocalDateTime.now());


        log.debug("供应商订单状态初始化完成，供应商: {}, 订单项数量: {}",
                supplierOrder.getSupplierId(), itemCount);
    }

    /**
     * 初始化订单项状态管理字段
     */
    private void initializeOrderItemStatus(TzOrderItem orderItem) {
        // 设置初始状态
        orderItem.setStatus(TzOrderItemStatusEnum.PENDING);

        // 初始化状态管理字段
        orderItem.setStatusUpdatedAt(LocalDateTime.now());
        // expectedArrivalDate 和 actualCompletionDate 在后续业务流程中设置

        // 设置时间字段
        LocalDateTime now = LocalDateTime.now();
        orderItem.setGmtCreated(now);
        orderItem.setGmtModified(now);

        log.debug("订单项状态初始化完成，SKU: {}", orderItem.getProductSkuId());
    }

    /**
     * 查找订单项对应的供应商订单
     */
    private TzOrderSupplier findMatchedSupplierOrder(TzOrderItem orderItem, List<TzOrderSupplier> supplierOrders) {
        return supplierOrders.stream()
                .filter(supplier -> supplier.getId().equals(orderItem.getSupplierOrderId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 更新状态管理统计字段
     */
    private void updateStatusManagementFields(TzOrderPurchase purchaseOrder,
                                            List<TzOrderSupplier> supplierOrders,
                                            List<TzOrderItem> orderItems) {
        // 更新采购订单的统计字段
        purchaseOrder.setSupplierCount(supplierOrders.size());
        purchaseOrder.setLineItemCount(orderItems.size());

        // 更新供应商订单的统计字段
        for (TzOrderSupplier supplierOrder : supplierOrders) {
            long itemCount = orderItems.stream()
                    .filter(item -> supplierOrder.getId().equals(item.getSupplierOrderId()))
                    .count();
            supplierOrder.setLineItemCount((int) itemCount);
        }

        // 批量更新数据库
        this.updateById(purchaseOrder);
        for (TzOrderSupplier supplierOrder : supplierOrders) {
            orderSupplierMapper.updateById(supplierOrder);
        }

        log.debug("状态管理统计字段更新完成");
    }

    /**
     * 触发状态计算和同步
     */
    private void triggerStatusCalculation(Long purchaseOrderId) {
        try {
            // 异步触发状态计算，避免影响订单创建性能
            statusSyncService.syncPurchaseOrderStatus(purchaseOrderId);
            log.debug("状态计算已触发，采购订单ID: {}", purchaseOrderId);
        } catch (Exception e) {
            log.warn("触发状态计算失败，采购订单ID: {}, 错误: {}", purchaseOrderId, e.getMessage());
            // 状态计算失败不应该影响订单创建，只记录警告日志
        }
    }

    /**
     * 清理购物车数据
     */
    private void cleanupShoppingCart(List<Long> shoppingCartIds) {
        if (!CollectionUtils.isEmpty(shoppingCartIds)) {
            try {
                shoppingCartMapper.deleteByIds(shoppingCartIds);
                log.info("购物车清理完成，删除数量: {}", shoppingCartIds.size());
            } catch (Exception e) {
                log.warn("清理购物车失败，购物车IDs: {}, 错误: {}", shoppingCartIds, e.getMessage());
                // 购物车清理失败不应该影响订单创建，只记录警告日志
            }
        }
    }

}
