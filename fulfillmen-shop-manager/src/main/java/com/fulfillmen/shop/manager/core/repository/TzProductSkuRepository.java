package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import java.util.List;

/**
 * 产品sku仓储层
 *
 * <AUTHOR>
 * @date 2025/7/7 10:07
 * @description: todo
 * @since 1.0.0
 */
public interface TzProductSkuRepository extends IRepository<TzProductSku> {

    /**
     * 批量插入sku
     *
     * @param skus sku列表
     * @return 插入的记录数
     */
    boolean batchInsertSkus(List<TzProductSku> skus);
}
