/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单事件处理Manager
 *
 * <AUTHOR>
 * @date 2025/6/26
 * @description 处理订单相关的异步事件和状态流转
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventManager {

    private final IOrderManager orderManager;
    private final ApplicationEventPublisher eventPublisher;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final TenantWarehouseMapper tenantWarehouseMapper;

    /**
     * 异步处理订单创建事件
     *
     * @param orderContext 订单上下文
     */
    public void handleOrderCreateEvent(OrderContext orderContext) {

        log.info("开始异步处理订单创建事件，采购订单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());

        for (TzOrderSupplier supplierOrder : orderContext.getSupplierOrders()) {
            try {
                // 异步提交供应商订单到外部平台
                submitSupplierOrderToExternal(supplierOrder, orderContext);

                // 发布订单状态变更事件
                publishOrderStatusChangeEvent(supplierOrder, TzOrderSupplierStatusEnum.PAID, "订单提交成功");

            } catch (Exception e) {
                log.error("提交供应商订单失败，订单号: {}", supplierOrder.getSupplierOrderNo(), e);

                // 更新订单状态为失败
                handleOrderFailure(supplierOrder, e.getMessage());
            }
        }

        log.info("订单创建事件处理完成，采购订单号: {}", orderContext.getPurchaseOrder().getPurchaseOrderNo());
    }

    /**
     * 提交供应商订单到外部平台
     */
    private void submitSupplierOrderToExternal(TzOrderSupplier supplierOrder, OrderContext orderContext) {
        log.info("开始提交供应商订单到外部平台，订单号: {}", supplierOrder.getSupplierOrderNo());

        // 构建1688订单创建请求
        OrderCreateRequestRecord request = buildOrderCreateRequest(supplierOrder, orderContext);

        // 调用1688订单创建API
        CreateOrderRespDTO createOrderRespDTO = orderManager.createCrossOrder(request);
        // 更新外部订单信息
        updateExternalOrderInfo(supplierOrder, createOrderRespDTO);
        log.info("供应商订单提交成功，外部订单ID: {}", createOrderRespDTO.getOrderId());
    }

    /**
     * 构建1688订单创建请求
     */
    private OrderCreateRequestRecord buildOrderCreateRequest(TzOrderSupplier supplierOrder,
        OrderContext orderContext) {
        log.debug("构建1688订单创建请求，供应商订单ID: {}", supplierOrder.getId());
        // 通过租户本地线程上下文，获取默认仓库地址。
        EnhancedTenantContext.TenantWarehouseInfo defaultWarehouse = EnhancedTenantContextHolder
            .getCurrentDefaultWarehouse();
        TenantWarehouse tenantWarehouse = null;
        // 如果默认仓库为空，则从数据库中获取
        if (defaultWarehouse == null) {
            // 1. 获取租户默认仓库地址
            LambdaQueryWrapper<TenantWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            tenantWarehouse = tenantWarehouseMapper.selectOne(
                queryWrapper.eq(TenantWarehouse::getTenantId, UserContextHolder.getTenantId())
                    .eq(TenantWarehouse::getIsDefault, 1));
            if (tenantWarehouse == null) {
                // 请设置租户默认仓库地址，否则无法创建订单
                log.error("请设置租户默认仓库地址，租户ID: {}", UserContextHolder.getTenantId());
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.TENANT_WAREHOUSE_NOT_FOUND);
            }
            defaultWarehouse = EnhancedTenantContext.TenantWarehouseInfo.convertFrom(tenantWarehouse);
        }
        // 2. 使用默认收货地址
        OrderCreateRequestRecord.AddressParamRecord addressParam = OrderCreateRequestRecord.AddressParamRecord.builder()
            // 读取用户姓名
            .fullName(defaultWarehouse.getContactName())
            // 读取用户手机号
            .mobile(defaultWarehouse.getContactMobile())
            // 读取用户邮编
            .postCode(defaultWarehouse.getPostcode())
            // 读取用户城市
            .cityText(defaultWarehouse.getCity())
            // 读取用户省份
            .provinceText(defaultWarehouse.getProvince())
            // 读取用户区县
            .areaText(defaultWarehouse.getDistrict())
            // 读取用户详细地址
            .address(defaultWarehouse.getAddress())
            // 读取用户地区码
            .districtCode(defaultWarehouse.getDistrictCode())
            .build();

        // 3. 构建商品列表
        List<OrderCreateRequestRecord.CargoParamRecord> cargoList = buildCargoList(supplierOrder);
        String outOrderId = null;
        if (UserContextHolder.getWmsCusCode() != null) {
            outOrderId = String.format("%s-%s", UserContextHolder.getWmsCusCode(), supplierOrder.getSupplierOrderNo());
        } else {
            outOrderId = String.format("%s-%s", EnhancedTenantContextHolder.getCurrentTenantId(), supplierOrder.getSupplierOrderNo());
        }
        return OrderCreateRequestRecord.builder()
            .flow("general")
            .addressParam(addressParam)
            .cargoParamList(cargoList)
            .outOrderId(outOrderId)
            .build();
    }

    /**
     * 根据供应商订单构建商品列表
     */
    private List<OrderCreateRequestRecord.CargoParamRecord> buildCargoList(TzOrderSupplier supplierOrder) {
        // 查询供应商订单对应的商品明细
        List<TzOrderItem> orderItems = orderItemMapper.selectList(
            new LambdaQueryWrapper<TzOrderItem>().eq(TzOrderItem::getSupplierOrderId, supplierOrder.getId()));

        return orderItems.stream()
            .map(orderItem -> {
                //
                String specId = orderItem.getIsSignleItem() == TzProductSpuSingleItemEnum.YES ? null
                    : orderItem.getPlatformSpecId();
                // 如果是单品，则不需要设置specId
                // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                // .openOfferId(orderItem.getOpenOfferId())
                // .outMemberId(orderItem.getOutMemberId())
                return OrderCreateRequestRecord.CargoParamRecord.builder()
                    .offerId(Long.valueOf(orderItem.getPlatformProductId()))
                    // 如果是单品，则不需要设置specId
                    .specId(specId)
                    .quantity(Double.valueOf(orderItem.getQuantity()))
                    // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                    // .openOfferId(orderItem.getOpenOfferId())
                    // .outMemberId(orderItem.getOutMemberId())
                    .build();
            })
            .collect(Collectors.toList());
    }

    /**
     * 更新外部订单信息
     */
    private void updateExternalOrderInfo(TzOrderSupplier supplierOrder, CreateOrderRespDTO response) {
        supplierOrder.setExternalPlatformOrderId(response.getOrderId());
        supplierOrder.setMetadataJson(JacksonUtil.toJsonString(response));
        // 保存到数据库
        orderSupplierMapper.updateById(supplierOrder);
        log.info("更新外部订单信息完成，订单号: {}, 外部订单ID: {}", supplierOrder.getSupplierOrderNo(), response.getOrderId());
    }

    /**
     * 处理订单失败
     */
    private void handleOrderFailure(TzOrderSupplier supplierOrder, String errorMessage) {
        // 更新订单状态为取消
        publishOrderStatusChangeEvent(supplierOrder, TzOrderSupplierStatusEnum.CANCELLED, errorMessage);

        // TODO: 记录失败原因，可能需要人工处理
        log.error("订单处理失败，需要人工干预，订单号: {}, 错误信息: {}", supplierOrder.getSupplierOrderNo(), errorMessage);
    }

    /**
     * 发布订单状态变更事件
     */
    private void publishOrderStatusChangeEvent(TzOrderSupplier supplierOrder, TzOrderSupplierStatusEnum newStatus,
        String reason) {

        OrderStatusChangeEvent event = OrderStatusChangeEvent.builder()
            .orderId(supplierOrder.getId())
            .orderType("SUPPLIER_ORDER")
            .oldStatus(supplierOrder.getStatus().ordinal())
            .newStatus(newStatus.ordinal())
            .reason(reason)
            .changeTime(LocalDateTime.now())
            .extData(Map.of("supplierOrderNo", supplierOrder.getSupplierOrderNo(), "supplierId",
                supplierOrder.getSupplierId(), "supplierName", supplierOrder.getSupplierName()))
            .build();

        eventPublisher.publishEvent(event);
        log.debug("发布订单状态变更事件: {}", event);
    }

    /**
     * 订单状态变更事件
     */
    @Getter
    public static class OrderStatusChangeEvent {

        // Getters
        private Long orderId;
        private String orderType;
        private Integer oldStatus;
        private Integer newStatus;
        private String reason;
        private LocalDateTime changeTime;
        private Map<String, Object> extData;

        // Builder pattern
        public static OrderStatusChangeEventBuilder builder() {
            return new OrderStatusChangeEventBuilder();
        }

        @Override
        public String toString() {
            return String.format("OrderStatusChangeEvent{orderId=%d, orderType='%s', %d->%d, reason='%s'}", orderId,
                orderType, oldStatus, newStatus, reason);
        }

        public static class OrderStatusChangeEventBuilder {

            private Long orderId;
            private String orderType;
            private Integer oldStatus;
            private Integer newStatus;
            private String reason;
            private LocalDateTime changeTime;
            private Map<String, Object> extData;

            public OrderStatusChangeEventBuilder orderId(Long orderId) {
                this.orderId = orderId;
                return this;
            }

            public OrderStatusChangeEventBuilder orderType(String orderType) {
                this.orderType = orderType;
                return this;
            }

            public OrderStatusChangeEventBuilder oldStatus(Integer oldStatus) {
                this.oldStatus = oldStatus;
                return this;
            }

            public OrderStatusChangeEventBuilder newStatus(Integer newStatus) {
                this.newStatus = newStatus;
                return this;
            }

            public OrderStatusChangeEventBuilder reason(String reason) {
                this.reason = reason;
                return this;
            }

            public OrderStatusChangeEventBuilder changeTime(LocalDateTime changeTime) {
                this.changeTime = changeTime;
                return this;
            }

            public OrderStatusChangeEventBuilder extData(Map<String, Object> extData) {
                this.extData = extData;
                return this;
            }

            public OrderStatusChangeEvent build() {
                OrderStatusChangeEvent event = new OrderStatusChangeEvent();
                event.orderId = this.orderId;
                event.orderType = this.orderType;
                event.oldStatus = this.oldStatus;
                event.newStatus = this.newStatus;
                event.reason = this.reason;
                event.changeTime = this.changeTime;
                event.extData = this.extData;
                return event;
            }
        }
    }
}
