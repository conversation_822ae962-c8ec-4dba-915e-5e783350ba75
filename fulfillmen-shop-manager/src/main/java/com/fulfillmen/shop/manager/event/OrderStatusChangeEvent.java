/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.event;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订单状态变更事件 - Manager层
 *
 * <pre>
 * 当订单状态发生变化时发布此事件，用于：
 * 1. 通知相关系统和服务
 * 2. 记录状态变更日志
 * 3. 触发后续业务流程
 * 4. 发送用户通知
 * 5. 更新缓存和统计数据
 * 6. 集成外部系统
 *
 * 架构定位：
 * - Manager层负责业务事件的定义和发布
 * - 事件包含完整的业务上下文信息
 * - 支持异步和同步的事件处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态变更事件，用于事件驱动的业务流程
 * @since 1.0.0
 */
@Getter
public class OrderStatusChangeEvent extends ApplicationEvent {

    /**
     * 采购订单ID
     */
    private final Long purchaseOrderId;

    /**
     * 原始状态
     */
    private final TzOrderPurchaseStatusEnum originalStatus;

    /**
     * 新状态
     */
    private final TzOrderPurchaseStatusEnum newStatus;

    /**
     * 状态变更原因
     */
    private final String changeReason;

    /**
     * 状态变更时间
     */
    private final LocalDateTime changeTime;

    /**
     * 事件类型
     */
    private final EventType eventType;

    /**
     * 构造函数
     *
     * @param purchaseOrderId 采购订单ID
     * @param originalStatus 原始状态
     * @param newStatus 新状态
     * @param changeReason 变更原因
     * @param changeTime 变更时间
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
                                TzOrderPurchaseStatusEnum originalStatus,
                                TzOrderPurchaseStatusEnum newStatus,
                                String changeReason,
                                LocalDateTime changeTime) {
        super(purchaseOrderId);
        this.purchaseOrderId = purchaseOrderId;
        this.originalStatus = originalStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.changeTime = changeTime;
        this.eventType = determineEventType(originalStatus, newStatus);
    }

    /**
     * 简化构造函数，使用当前时间
     */
    public OrderStatusChangeEvent(Long purchaseOrderId,
                                TzOrderPurchaseStatusEnum originalStatus,
                                TzOrderPurchaseStatusEnum newStatus,
                                String changeReason) {
        this(purchaseOrderId, originalStatus, newStatus, changeReason, LocalDateTime.now());
    }

    /**
     * 判断是否为关键状态变更
     */
    public boolean isCriticalStatusChange() {
        return eventType == EventType.PAYMENT_COMPLETED ||
               eventType == EventType.ORDER_COMPLETED ||
               eventType == EventType.ORDER_CANCELLED;
    }

    /**
     * 判断是否需要用户通知
     */
    public boolean shouldNotifyUser() {
        return eventType == EventType.PAYMENT_COMPLETED ||
               eventType == EventType.SHIPPED ||
               eventType == EventType.ORDER_COMPLETED ||
               eventType == EventType.ORDER_CANCELLED;
    }

    /**
     * 获取用户友好的状态变更描述
     */
    public String getUserFriendlyDescription() {
        return switch (eventType) {
            case PAYMENT_COMPLETED -> "支付成功，我们正在为您准备商品";
            case PROCUREMENT_STARTED -> "开始采购您的商品";
            case SHIPPED -> "商品已发货，正在运输途中";
            case DELIVERED_TO_WAREHOUSE -> "商品已到达仓库，正在质检中";
            case ORDER_COMPLETED -> "订单已完成，感谢您的购买";
            case ORDER_CANCELLED -> "订单已取消";
            case PARTIAL_FULFILLMENT -> "部分商品已处理完成";
            case STATUS_PROGRESSION -> String.format("订单状态已更新：%s", newStatus.getDescription());
        };
    }

    /**
     * 确定事件类型
     */
    private EventType determineEventType(TzOrderPurchaseStatusEnum originalStatus, TzOrderPurchaseStatusEnum newStatus) {
        if (newStatus == TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
            return EventType.ORDER_CANCELLED;
        }

        return switch (newStatus) {
            case PAYMENT_COMPLETED -> EventType.PAYMENT_COMPLETED;
            case PROCUREMENT_IN_PROGRESS -> EventType.PROCUREMENT_STARTED;
            case PARTIALLY_FULFILLED -> EventType.PARTIAL_FULFILLMENT;
            case SUPPLIER_SHIPPED -> EventType.SHIPPED;
            case WAREHOUSE_RECEIVED -> EventType.DELIVERED_TO_WAREHOUSE;
            case IN_STOCK -> EventType.ORDER_COMPLETED;
            default -> EventType.STATUS_PROGRESSION;
        };
    }

    /**
     * 事件类型枚举
     */
    @Getter
    public enum EventType {
        /**
         * 支付完成
         */
        PAYMENT_COMPLETED("支付完成"),

        /**
         * 开始采购
         */
        PROCUREMENT_STARTED("开始采购"),

        /**
         * 部分履约
         */
        PARTIAL_FULFILLMENT("部分履约"),

        /**
         * 已发货
         */
        SHIPPED("已发货"),

        /**
         * 已送达仓库
         */
        DELIVERED_TO_WAREHOUSE("已送达仓库"),

        /**
         * 订单完成
         */
        ORDER_COMPLETED("订单完成"),

        /**
         * 订单取消
         */
        ORDER_CANCELLED("订单取消"),

        /**
         * 一般状态进展
         */
        STATUS_PROGRESSION("状态进展");

        private final String description;

        EventType(String description) {
            this.description = description;
        }

    }

    @Override
    public String toString() {
        return String.format("OrderStatusChangeEvent{orderId=%d, %s -> %s, reason='%s', type=%s, time=%s}",
                purchaseOrderId,
                originalStatus.getDescription(),
                newStatus.getDescription(),
                changeReason,
                eventType,
                changeTime);
    }
}
