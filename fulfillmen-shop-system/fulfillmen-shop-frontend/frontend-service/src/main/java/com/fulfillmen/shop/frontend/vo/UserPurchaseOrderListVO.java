/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 用户订单列表视图对象
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 用户端订单列表显示的数据
 */
@Data
@Schema(description = "用户订单列表")
public class UserOrderListVO {

    @Schema(description = "采购单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单状态")
    private TzOrderPurchaseStatusEnum status;

    @Schema(description = "订单状态显示名称")
    private TzOrderPurchaseStatusEnum statusName;

    @Schema(description = "订单总金额")
    private BigDecimal totalAmount;

    @Schema(description = "订单商品数量")
    private Integer itemCount;

    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "订单商品信息（小窗口显示购买商品列表）")
    private List<OrderItemInfo> items;

    @Schema(description = "是否可以取消")
    private Boolean canCancel;

    @Schema(description = "是否可以确认收货")
    private Boolean canConfirmReceipt;

    @Schema(description = "是否可以申请退款")
    private Boolean canApplyRefund;

    /**
     * 订单商品信息
     */
    @Data
    @Schema(description = "订单商品信息")
    public static class OrderItemInfo {

        @Schema(description = "订单项ID")
        private String id;

        @Schema(description = "商品图片")
        private String productImage;

        @Schema(description = "商品标题")
        private String productTitle;

        @Schema(description = "商品标题(英文)")
        private String productTitleEn;

        @Schema(description = "商品规格")
        private List<AttrJson> skuSpecs;

        @Schema(description = "商品数量")
        private Integer quantity;

        @Schema(description = "商品单价")
        private BigDecimal price;
    }
}
