/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.util;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;

/**
 * 订单状态工具类
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 处理订单状态转换和权限判断
 */
public class OrderStatusUtil {

    /**
     * 获取用户友好的状态显示名称
     */
    public static String getUserFriendlyStatusName(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "未知状态";
        }

        return switch (status) {
            case TEMPORARILY_SAVED -> "订单保存中";
            case PAYMENT_PENDING -> "待支付";
            case PAYMENT_COMPLETED -> "支付成功";
            case PROCUREMENT_IN_PROGRESS -> "处理中";
            case PARTIALLY_FULFILLED -> "部分履约";
            case SUPPLIER_SHIPPED -> "商品发货中";
            case WAREHOUSE_PENDING_RECEIVED -> "运输中";
            case WAREHOUSE_RECEIVED -> "已到仓库";
            case IN_STOCK -> "已完成";
            case ORDER_CANCELLED -> "已取消";
            default -> status.getDescription();
        };
    }

    /**
     * 获取用户友好的状态描述
     */
    public static String getUserFriendlyStatusDescription(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "";
        }

        switch (status) {
            case TEMPORARILY_SAVED:
                return "订单已保存，等待进一步处理";
            case PAYMENT_PENDING:
                return "请尽快完成支付";
            case PAYMENT_COMPLETED:
                return "支付成功，我们正在为您准备商品";
            case PROCUREMENT_IN_PROGRESS:
                return "正在为您采购商品";
            case PARTIALLY_FULFILLED:
                return "部分商品已处理完成";
            case SUPPLIER_SHIPPED:
                return "商品已发往我们的仓库";
            case WAREHOUSE_PENDING_RECEIVED:
                return "商品正在运输途中";
            case WAREHOUSE_RECEIVED:
                return "商品已到达仓库，正在质检中";
            case IN_STOCK:
                return "订单已完成，感谢您的购买";
            case ORDER_CANCELLED:
                return "订单已取消";
            default:
                return "";
        }
    }

    /**
     * 判断订单是否可以取消
     */
    public static boolean canCancel(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isCancellable();
    }

    /**
     * 判断订单是否可以支付
     */
    public static boolean canPay(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isPayable();
    }

    /**
     * 判断订单是否可以确认收货
     * 注意：这个方法适用于从WMS发货给客户后的确认收货
     * 现在的状态枚举是到WMS入库为止，所以这里返回false
     * 后续如果有发货给客户的状态，需要相应调整
     */
    public static boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        // 当前状态枚举只到WMS入库，没有发货给客户的状态
        // 如果后续需要客户确认收货功能，需要扩展状态枚举
        return false;
    }

    /**
     * 判断订单是否可以申请退款
     */
    public static boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到完成前的状态都可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED ||
            status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 获取订单进度百分比
     */
    public static int getProgressPercentage(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return 0;
        }

        switch (status) {
            case TEMPORARILY_SAVED:
                return 5;
            case PAYMENT_PENDING:
                return 10;
            case PAYMENT_COMPLETED:
                return 20;
            case PROCUREMENT_IN_PROGRESS:
                return 30;
            case PARTIALLY_FULFILLED:
                return 40;
            case SUPPLIER_SHIPPED:
                return 50;
            case WAREHOUSE_PENDING_RECEIVED:
                return 70;
            case WAREHOUSE_RECEIVED:
                return 85;
            case IN_STOCK:
                return 100;
            case ORDER_CANCELLED:
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 判断订单是否为最终状态
     */
    public static boolean isFinalStatus(TzOrderPurchaseStatusEnum status) {
        return status == TzOrderPurchaseStatusEnum.IN_STOCK ||
            status == TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
    }
}
