package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.frontend.vo.UserOrderListVO;
import com.fulfillmen.shop.frontend.vo.UserOrderListVO.OrderItemInfo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 前端采购订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/7 16:16
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface FrontendOrderPurchaseConvert {

    FrontendOrderPurchaseConvert INSTANCE = Mappers.getMapper(FrontendOrderPurchaseConvert.class);

    /**
     * 转换采购订单分页对象为前端订单列表分页对象
     *
     * @param orderPurchasePage              采购订单分页对象
     * @return 前端订单列表分页对象
     */
    PageDTO<UserOrderListVO> convertToUserOrderListVOPage(Page<TzOrderPurchase> orderPurchasePage);

    /**
     * 转换采购订单为前端订单详情
     *
     * @param orderItem    订单项列表
     * @return 前端订单详情
     */
    UserOrderListVO.OrderItemInfo convertToOrderItemInfo(TzOrderItem orderItem);

    /**
     * 转换订单项列表为前端订单项列表
     *
     * @param tzOrderItems 订单项列表
     * @return 前端订单项列表
     */
    List<OrderItemInfo> convertToOrderItemInfoList(List<TzOrderItem> tzOrderItems);
}
