/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO.OrderItemInfo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 前端采购订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/7 16:16
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface FrontendOrderPurchaseConvert {

    FrontendOrderPurchaseConvert INSTANCE = Mappers.getMapper(FrontendOrderPurchaseConvert.class);

    /**
     * 转换采购订单分页对象为前端订单列表分页对象
     *
     * @param orderPurchasePage 采购订单分页对象
     * @return 前端订单列表分页对象
     */
    PageDTO<UserPurchaseOrderListVO> convertToUserPurchaseOrderListVO(Page<TzOrderPurchase> orderPurchasePage);

    /**
     * 转换订单项为前端订单项信息
     *
     * @param orderItem 订单项
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfo(TzOrderItem orderItem);

    /**
     * 转换采购订单为前端订单详情
     *
     * @param tzOrderPurchase 采购订单
     * @return 前端订单详情
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrderNo"),
        @Mapping(target = "status", source = "orderStatus"),
        @Mapping(target = "statusName", source = "orderStatus"),
        @Mapping(target = "itemCount", source = "lineItemCount"),
        @Mapping(target = "createTime", source = "gmtCreated"),
        @Mapping(target = "canCancel", expression = "java(canCancelOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "items", ignore = true)
    })
    UserPurchaseOrderListVO convertToUserPurchaseOrderListVO(TzOrderPurchase tzOrderPurchase);

    /**
     * 转换订单项列表为前端订单项列表
     *
     * @param tzOrderItems 订单项列表
     * @return 前端订单项列表
     */
    List<OrderItemInfo> convertToOrderItemInfoList(List<TzOrderItem> tzOrderItems);

    /**
     * 判断订单是否可以取消
     *
     * @param status 订单状态
     * @return 是否可以取消
     */
    default Boolean canCancelOrder(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 只有待支付、支付完成、采购中状态的订单可以取消
        return status == TzOrderPurchaseStatusEnum.PAYMENT_PENDING
            || status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED
            || status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
    }

    /**
     * 判断订单是否可以确认收货
     *
     * @param status 订单状态
     * @return 是否可以确认收货
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 只有仓库已收货状态的订单可以确认收货
        return status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
    }

    /**
     * 判断订单是否可以申请退款
     *
     * @param status 订单状态
     * @return 是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到已入库前的订单可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED
            || status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS
            || status == TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED
            || status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED
            || status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED
            || status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
    }
}
