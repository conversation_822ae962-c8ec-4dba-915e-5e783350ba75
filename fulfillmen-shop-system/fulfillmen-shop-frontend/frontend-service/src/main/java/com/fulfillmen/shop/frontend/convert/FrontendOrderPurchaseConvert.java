/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO.OrderItemInfo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 前端采购订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/7 16:16
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface FrontendOrderPurchaseConvert {

    FrontendOrderPurchaseConvert INSTANCE = Mappers.getMapper(FrontendOrderPurchaseConvert.class);

    /**
     * 转换采购订单分页对象为前端订单列表分页对象
     *
     * @param orderPurchasePage 采购订单分页对象
     * @return 前端订单列表分页对象
     */
    @Mappings({
        @Mapping(source = "current", target = "pageIndex"),
        @Mapping(source = "size", target = "pageSize")
    })
    PageDTO<UserPurchaseOrderListVO> convertToUserPurchaseOrderListVO(Page<TzOrderPurchase> orderPurchasePage);

    /**
     * 转换订单项为前端订单项信息
     *
     * @param orderItem 订单项
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "usdPrice", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), null))")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfo(TzOrderItem orderItem);

    /**
     * 转换订单项为前端订单项信息（带汇率）
     *
     * @param orderItem    订单项
     * @param exchangeRate 汇率
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "orderItem.productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "productTitle", source = "orderItem.productTitle"),
        @Mapping(target = "productTitleEn", source = "orderItem.productTitleEn"),
        @Mapping(target = "skuSpecs", source = "orderItem.skuSpecs"),
        @Mapping(target = "quantity", source = "orderItem.quantity"),
        @Mapping(target = "price", source = "orderItem.price"),
        @Mapping(target = "usdPrice", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), exchangeRate))")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfoWithExchangeRate(TzOrderItem orderItem, BigDecimal exchangeRate);

    /**
     * 转换采购订单为前端订单详情
     *
     * @param tzOrderPurchase 采购订单
     * @return 前端订单详情
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrderNo"),
        @Mapping(target = "status", source = "orderStatus"),
        @Mapping(target = "statusName", source = "orderStatus"),
        @Mapping(target = "itemCount", source = "lineItemCount"),
        @Mapping(target = "createTime", source = "gmtCreated"),
        @Mapping(target = "totalAmountUsd", expression = "java(calculateTotalAmountUsd(tzOrderPurchase.getTotalAmount(), tzOrderPurchase.getExchangeRate()))"),
        @Mapping(target = "canCancel", expression = "java(canCancelOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "items", ignore = true)
    })
    UserPurchaseOrderListVO convertToUserPurchaseOrderListVO(TzOrderPurchase tzOrderPurchase);

    /**
     * 转换订单项列表为前端订单项列表
     *
     * @param tzOrderItems 订单项列表
     * @return 前端订单项列表
     */
    List<OrderItemInfo> convertToOrderItemInfoList(List<TzOrderItem> tzOrderItems);

    /**
     * 判断订单是否可以取消
     *
     * @param status 订单状态
     * @return 是否可以取消
     */
    default Boolean canCancelOrder(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 只有待支付、支付完成、采购中状态的订单可以取消
        return status == TzOrderPurchaseStatusEnum.PAYMENT_PENDING
            || status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED
            || status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
    }

    /**
     * 判断订单是否可以确认收货
     *
     * @param status 订单状态
     * @return 是否可以确认收货
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 只有仓库已收货状态的订单可以确认收货
        return status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
    }

    /**
     * 判断订单是否可以申请退款
     *
     * @param status 订单状态
     * @return 是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到已入库前的订单可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED
            || status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS
            || status == TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED
            || status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED
            || status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED
            || status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
    }

    /**
     * 根据订单创建时的汇率计算美元金额
     *
     * @param totalAmount  订单总金额（人民币）
     * @param exchangeRate 订单创建时的汇率（CNY to USD）
     * @return 美元金额
     */
    default BigDecimal calculateTotalAmountUsd(BigDecimal totalAmount, BigDecimal exchangeRate) {
        if (totalAmount == null) {
            return BigDecimal.ZERO;
        }

        if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            // 如果没有保存汇率或汇率无效，使用当前汇率进行转换
            return CurrencyConversionUtils.convertCurrency(totalAmount, "CNY", "USD");
        }

        // 使用订单创建时保存的汇率进行转换，保留两位小数并截断
        return totalAmount.multiply(exchangeRate).setScale(2, RoundingMode.DOWN);
    }

    /**
     * 计算订单项的美元价格
     *
     * @param price        商品单价（人民币）
     * @param exchangeRate 汇率（CNY to USD），如果为null则使用当前汇率
     * @return 美元价格
     */
    default BigDecimal calculateItemUsdPrice(BigDecimal price, BigDecimal exchangeRate) {
        if (price == null) {
            return BigDecimal.ZERO;
        }

        if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            // 如果没有汇率信息，使用当前汇率进行转换
            return CurrencyConversionUtils.convertCurrency(price, "CNY", "USD");
        }

        // 使用指定汇率进行转换，保留两位小数并截断
        return price.multiply(exchangeRate).setScale(2, RoundingMode.DOWN);
    }
}
