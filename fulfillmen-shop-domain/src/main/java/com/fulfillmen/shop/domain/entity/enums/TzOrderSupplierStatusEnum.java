/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 供应商订单履约状态枚举
 *
 * <AUTHOR>
 * @date 2025/6/24
 * @description 供应商订单履约状态枚举
 * @since 1.0.0
 */
@Getter
public enum TzOrderSupplierStatusEnum {

    /**
     * 待支付
     * <pre>
     * 财务确认支付前，订单状态变为待支付。
     * </pre>
     */
    PENDING_PAYMENT(0, "待支付"),

    /**
     * 已支付
     * <pre>
     * 财务确认支付后，订单状态变为已支付。
     * </pre>
     */
    PAID(1, "已支付"),

    /**
     * 采购中
     * <pre>
     * 财务确认支付后，开始向供应商下单采购。
     * </pre>
     */
    PROCUREMENT_IN_PROGRESS(2, "采购中"),

    /**
     * 待发货
     * <pre>
     * 供应商确认订单，准备发货。
     * </pre>
     */
    PENDING_SHIPMENT(3, "待发货"),

    /**
     * 部分发货
     * <pre>
     * 供应商部分商品已发货，还有商品待发货。
     * </pre>
     */
    PARTIALLY_SHIPPED(4, "部分发货"),

    /**
     * 已发货
     * <pre>
     * 供应商所有商品都已发货。
     * </pre>
     */
    SHIPPED(5, "已发货"),

    /**
     * 已送达仓库
     * <pre>
     * 货物已送达WMS仓库，等待质检入库。
     * </pre>
     */
    DELIVERED_TO_WAREHOUSE(6, "已送达仓库"),

    /**
     * 已完成
     * <pre>
     * 货物已完成质检并入库，供应商订单履约完成。
     * </pre>
     */
    COMPLETED(7, "已完成"),

    /**
     * 已取消
     * <pre>
     * 订单被取消，需要处理退款等后续事宜。
     * </pre>
     */
    CANCELLED(8, "已取消");

    @EnumValue
    @JsonValue
    private final Integer value;

    private final String description;

    TzOrderSupplierStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @JsonCreator
    public static TzOrderSupplierStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TzOrderSupplierStatusEnum status : TzOrderSupplierStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     */
    public static TzOrderSupplierStatusEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (TzOrderSupplierStatusEnum status : TzOrderSupplierStatusEnum.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }
}
