/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.service;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单状态计算器
 *
 * <pre>
 * 核心功能：
 * 1. 根据供应商订单状态计算采购订单状态
 * 2. 根据订单项状态计算供应商订单状态
 * 3. 提供状态流转验证和规则检查
 * 
 * 设计原则：
 * - 状态计算基于聚合规则，确保上层状态反映下层状态的整体情况
 * - 支持部分完成场景，提供细粒度的状态跟踪
 * - 状态计算具有幂等性，多次计算结果一致
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态计算器，实现多层级状态的自动计算逻辑
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderStatusCalculator {

    /**
     * 计算采购订单状态
     * 
     * <pre>
     * 计算规则：
     * 1. 如果所有供应商订单都取消 -> 已取消
     * 2. 如果存在待支付的供应商订单 -> 待支付
     * 3. 如果所有供应商订单都已完成 -> 已入库
     * 4. 如果所有供应商订单都已送达仓库或完成 -> 仓库已收货
     * 5. 如果所有供应商订单都已发货或更高状态 -> 供应商已发货
     * 6. 如果部分供应商订单已完成 -> 部分履约
     * 7. 如果所有供应商订单都已支付或更高状态 -> 采购中
     * 8. 其他情况 -> 支付完成
     * </pre>
     *
     * @param supplierStatuses 供应商订单状态列表
     * @return 计算得出的采购订单状态
     */
    public TzOrderPurchaseStatusEnum calculatePurchaseOrderStatus(List<TzOrderSupplierStatusEnum> supplierStatuses) {
        if (CollectionUtils.isEmpty(supplierStatuses)) {
            log.warn("供应商订单状态列表为空，返回默认状态：待支付");
            return TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
        }

        log.debug("开始计算采购订单状态，供应商订单数量: {}, 状态分布: {}", 
                supplierStatuses.size(), getStatusDistribution(supplierStatuses));

        // 统计各状态数量
        Map<TzOrderSupplierStatusEnum, Long> statusCount = supplierStatuses.stream()
                .collect(Collectors.groupingBy(status -> status, Collectors.counting()));

        int totalCount = supplierStatuses.size();
        
        // 1. 如果所有供应商订单都取消了
        if (statusCount.getOrDefault(TzOrderSupplierStatusEnum.CANCELLED, 0L) == totalCount) {
            return TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
        }

        // 2. 如果还有待支付的
        if (statusCount.containsKey(TzOrderSupplierStatusEnum.PENDING_PAYMENT)) {
            return TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
        }

        // 3. 如果所有都已完成
        if (statusCount.getOrDefault(TzOrderSupplierStatusEnum.COMPLETED, 0L) == totalCount) {
            return TzOrderPurchaseStatusEnum.IN_STOCK;
        }

        // 4. 如果所有都已送达仓库或完成
        long deliveredOrCompletedCount = statusCount.getOrDefault(TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE, 0L) +
                                       statusCount.getOrDefault(TzOrderSupplierStatusEnum.COMPLETED, 0L);
        if (deliveredOrCompletedCount == totalCount) {
            return TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
        }

        // 5. 如果所有都已发货或更高状态
        long shippedOrHigherCount = supplierStatuses.stream()
                .mapToInt(status -> status.getValue())
                .filter(value -> value >= TzOrderSupplierStatusEnum.SHIPPED.getValue())
                .count();
        if (shippedOrHigherCount == totalCount) {
            return TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED;
        }

        // 6. 如果部分已完成（存在完成状态，但不是全部）
        if (statusCount.getOrDefault(TzOrderSupplierStatusEnum.COMPLETED, 0L) > 0) {
            return TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED;
        }

        // 7. 如果所有都已支付或更高状态
        long paidOrHigherCount = supplierStatuses.stream()
                .mapToInt(status -> status.getValue())
                .filter(value -> value >= TzOrderSupplierStatusEnum.PAID.getValue() && 
                               value != TzOrderSupplierStatusEnum.CANCELLED.getValue())
                .count();
        if (paidOrHigherCount == totalCount) {
            return TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
        }

        // 8. 默认返回支付完成
        return TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
    }

    /**
     * 计算供应商订单状态
     * 
     * <pre>
     * 计算规则：
     * 1. 如果所有订单项都已完成 -> 已完成
     * 2. 如果所有订单项都已送达或完成 -> 已送达仓库
     * 3. 如果存在已发货的订单项 -> 已发货
     * 4. 如果存在采购中的订单项 -> 采购中
     * 5. 如果存在失败的订单项 -> 需要人工处理
     * 6. 其他情况 -> 已支付
     * </pre>
     *
     * @param itemStatuses 订单项状态列表
     * @return 计算得出的供应商订单状态
     */
    public TzOrderSupplierStatusEnum calculateSupplierOrderStatus(List<TzOrderItemStatusEnum> itemStatuses) {
        if (CollectionUtils.isEmpty(itemStatuses)) {
            log.warn("订单项状态列表为空，返回默认状态：待支付");
            return TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        }

        log.debug("开始计算供应商订单状态，订单项数量: {}, 状态分布: {}", 
                itemStatuses.size(), getItemStatusDistribution(itemStatuses));

        // 统计各状态数量
        Map<TzOrderItemStatusEnum, Long> statusCount = itemStatuses.stream()
                .collect(Collectors.groupingBy(status -> status, Collectors.counting()));

        int totalCount = itemStatuses.size();

        // 1. 如果所有订单项都已完成
        if (statusCount.getOrDefault(TzOrderItemStatusEnum.COMPLETED, 0L) == totalCount) {
            return TzOrderSupplierStatusEnum.COMPLETED;
        }

        // 2. 如果所有订单项都已送达或完成
        long deliveredOrCompletedCount = statusCount.getOrDefault(TzOrderItemStatusEnum.DELIVERED, 0L) +
                                       statusCount.getOrDefault(TzOrderItemStatusEnum.COMPLETED, 0L);
        if (deliveredOrCompletedCount == totalCount) {
            return TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE;
        }

        // 3. 如果存在已发货的订单项
        if (statusCount.containsKey(TzOrderItemStatusEnum.SHIPPED)) {
            // 检查是否为部分发货
            long shippedOrHigherCount = itemStatuses.stream()
                    .mapToInt(status -> status.getCode())
                    .filter(code -> code >= TzOrderItemStatusEnum.SHIPPED.getCode() && 
                                  code != TzOrderItemStatusEnum.FAILED.getCode() &&
                                  code != TzOrderItemStatusEnum.CANCELLED.getCode())
                    .count();
            
            if (shippedOrHigherCount == totalCount) {
                return TzOrderSupplierStatusEnum.SHIPPED;
            } else {
                return TzOrderSupplierStatusEnum.PARTIALLY_SHIPPED;
            }
        }

        // 4. 如果存在采购中的订单项
        if (statusCount.containsKey(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS)) {
            return TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS;
        }

        // 5. 如果存在失败的订单项，需要特殊处理
        if (statusCount.containsKey(TzOrderItemStatusEnum.FAILED)) {
            log.warn("存在失败的订单项，需要人工处理，失败数量: {}", statusCount.get(TzOrderItemStatusEnum.FAILED));
            // 这里可以根据业务规则决定是返回特定状态还是继续处理
        }

        // 6. 默认返回已支付
        return TzOrderSupplierStatusEnum.PAID;
    }

    /**
     * 验证状态流转是否合法
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return 是否可以流转
     */
    public boolean isValidStatusTransition(TzOrderPurchaseStatusEnum fromStatus, TzOrderPurchaseStatusEnum toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        // 已取消和已完成状态不能再变更
        if (fromStatus == TzOrderPurchaseStatusEnum.ORDER_CANCELLED || 
            fromStatus == TzOrderPurchaseStatusEnum.IN_STOCK) {
            return false;
        }

        // 不能倒退到更早的状态（除了取消）
        if (toStatus != TzOrderPurchaseStatusEnum.ORDER_CANCELLED && 
            toStatus.getValue() < fromStatus.getValue()) {
            return false;
        }

        return true;
    }

    /**
     * 获取状态分布统计（用于日志）
     */
    private String getStatusDistribution(List<TzOrderSupplierStatusEnum> statuses) {
        return statuses.stream()
                .collect(Collectors.groupingBy(status -> status, Collectors.counting()))
                .toString();
    }

    /**
     * 获取订单项状态分布统计（用于日志）
     */
    private String getItemStatusDistribution(List<TzOrderItemStatusEnum> statuses) {
        return statuses.stream()
                .collect(Collectors.groupingBy(status -> status, Collectors.counting()))
                .toString();
    }
}
