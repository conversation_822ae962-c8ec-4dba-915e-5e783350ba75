/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商订单表-按供应商拆分的履约订单
 *
 * <AUTHOR>
 * @date 2025/6/25 15:11
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_supplier")
public class TzOrderSupplier implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 采购订单ID
     */
    @TableField(value = "purchase_order_id")
    private Long purchaseOrderId;

    /**
     * 供应商订单编号
     */
    @TableField(value = "supplier_order_no")
    private String supplierOrderNo;

    /**
     * 平台代码: 1688/TAOBAO/JD/PDD
     */
    @TableField(value = "platform_code")
    private PlatformCodeEnum platformCode;

    /**
     * 供应商ID
     */
    @TableField(value = "supplier_id")
    private String supplierId;

    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商店铺名称
     */
    @TableField(value = "supplier_shop_name")
    private String supplierShopName;

    /**
     * 平台下单后，返回的元数据。记录，用于还原和恢复。
     */
    @TableField(value = "metadata_json")
    private String metadataJson;

    /**
     * 是否产生了多个订单id,注：同一供应商，创建订单平台可能分配多个订单 ID。 默认 0 否，1 是
     */
    @TableField(value = "is_multiple_orders")
    private TzOrderSupplierMultipleOrdersEnum isMultipleOrders;

    /**
     * 外部平台订单ID
     */
    @TableField(value = "external_platform_order_id")
    private String externalPlatformOrderId;

    /**
     * 多个 orderId
     */
    @TableField(value = "external_platform_order_ids")
    private String externalPlatformOrderIds;

    /**
     * 外部平台订单号
     */
    @TableField(value = "external_platform_order_no")
    private String externalPlatformOrderNo;

    /**
     * 履约状态: 0待支付/1已支付/2待采购/3已采购/4待发货/5已发货/6已收货/7已完成/8已取消
     */
    @TableField(value = "status")
    private TzOrderSupplierStatusEnum status;

    /**
     * 下单时间
     */
    @TableField(value = "order_date")
    private LocalDateTime orderDate;

    /**
     * 支付时间
     */
    @TableField(value = "payment_date")
    private LocalDateTime paymentDate;

    /**
     * 采购时间
     */
    @TableField(value = "procurement_date")
    private LocalDateTime procurementDate;

    /**
     * 发货时间
     */
    @TableField(value = "shipped_date")
    private LocalDateTime shippedDate;

    /**
     * 交付时间
     */
    @TableField(value = "delivered_date")
    private LocalDateTime deliveredDate;

    /**
     * 完成时间
     */
    @TableField(value = "completed_date")
    private LocalDateTime completedDate;

    /**
     * 取消时间
     */
    @TableField(value = "cancelled_date")
    private LocalDateTime cancelledDate;

    /**
     * 商品金额
     */
    @TableField(value = "goods_amount")
    private BigDecimal goodsAmount;

    /**
     * 运费
     */
    @TableField(value = "freight_amount")
    private BigDecimal freightAmount;

    /**
     * 税费
     */
    @TableField(value = "tax_amount")
    private BigDecimal taxAmount;

    /**
     * 服务费 - tenant_info.service_fee 租户设定服务费率。
     */
    @TableField(value = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 应付金额(用户支付金额) = (freight_amount + goods_amount) * service_fee
     */
    @TableField(value = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 折扣金额/红包金额（此字段不展示给用户看）
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 实付金额（此字段不展示给用户看）
     */
    @TableField(value = "real_pay_amount")
    private BigDecimal realPayAmount;

    /**
     * 订单行数
     */
    @TableField(value = "line_item_count")
    private Integer lineItemCount;

    /**
     * 已完成的订单项数量
     */
    @TableField(value = "completed_item_count")
    private Integer completedItemCount;

    /**
     * 状态最后更新时间
     */
    @TableField(value = "status_updated_at")
    private LocalDateTime statusUpdatedAt;

    /**
     * 供应商备注
     */
    @TableField(value = "supplier_notes")
    private String supplierNotes;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;

    // ==================== 状态管理辅助方法 ====================

    /**
     * 获取订单项完成进度百分比
     */
    public double getItemCompletionPercentage() {
        if (lineItemCount == null || lineItemCount == 0) {
            return 0.0;
        }
        int completed = completedItemCount != null ? completedItemCount : 0;
        return (double) completed / lineItemCount * 100;
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return status == TzOrderSupplierStatusEnum.COMPLETED ||
               status == TzOrderSupplierStatusEnum.CANCELLED;
    }

    /**
     * 判断是否可以取消
     */
    public boolean isCancellable() {
        return !isFinalStatus();
    }

    /**
     * 更新状态信息
     */
    public void updateStatus(TzOrderSupplierStatusEnum newStatus, Integer totalItems, Integer completedItems) {
        this.status = newStatus;
        this.lineItemCount = totalItems;
        this.completedItemCount = completedItems;
        this.statusUpdatedAt = LocalDateTime.now();
        this.gmtModified = LocalDateTime.now();

        // 根据状态设置相应的时间字段
        switch (newStatus) {
            case PAID -> this.paymentDate = LocalDateTime.now();
            case SHIPPED -> this.shippedDate = LocalDateTime.now();
            case DELIVERED_TO_WAREHOUSE -> this.deliveredDate = LocalDateTime.now();
            case COMPLETED -> this.completedDate = LocalDateTime.now();
            case CANCELLED -> this.cancelledDate = LocalDateTime.now();
        }
    }

    /**
     * 获取当前状态的持续时间（分钟）
     */
    public long getStatusDurationMinutes() {
        if (statusUpdatedAt == null) {
            return 0;
        }
        return java.time.Duration.between(statusUpdatedAt, LocalDateTime.now()).toMinutes();
    }
}
