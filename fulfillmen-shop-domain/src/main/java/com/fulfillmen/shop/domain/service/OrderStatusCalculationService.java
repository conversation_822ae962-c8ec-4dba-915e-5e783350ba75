/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.service;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.model.OrderStatusCalculationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单状态计算服务
 *
 * <pre>
 * 提供高级的状态计算功能，包括：
 * 1. 带结果封装的状态计算
 * 2. 状态变化检测
 * 3. 计算原因分析
 * 4. 批量状态计算
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态计算服务，提供高级状态计算功能
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStatusCalculationService {

    private final OrderStatusCalculator statusCalculator;

    /**
     * 计算采购订单状态并返回详细结果
     *
     * @param currentStatus 当前状态
     * @param supplierStatuses 供应商订单状态列表
     * @return 计算结果
     */
    public OrderStatusCalculationResult calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum currentStatus,
            List<TzOrderSupplierStatusEnum> supplierStatuses) {

        log.debug("开始计算采购订单状态，当前状态: {}, 供应商订单数量: {}",
                currentStatus, supplierStatuses.size());

        // 使用核心计算器计算新状态
        TzOrderPurchaseStatusEnum newStatus = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);

        // 检查状态是否发生变化
        boolean statusChanged = currentStatus != newStatus;

        if (statusChanged) {
            String reason = generateCalculationReason(currentStatus, newStatus, supplierStatuses);
            log.info("采购订单状态发生变化: {} -> {}, 原因: {}", currentStatus, newStatus, reason);

            return OrderStatusCalculationResult.changed(currentStatus, newStatus, supplierStatuses, reason);
        } else {
            log.debug("采购订单状态无变化，保持: {}", currentStatus);
            return OrderStatusCalculationResult.unchanged(currentStatus, supplierStatuses);
        }
    }

    /**
     * 批量计算多个采购订单的状态
     *
     * @param orderStatusData 订单状态数据列表
     * @return 计算结果列表
     */
    public List<OrderStatusCalculationResult> batchCalculatePurchaseOrderStatus(
            List<PurchaseOrderStatusData> orderStatusData) {

        log.info("开始批量计算采购订单状态，订单数量: {}", orderStatusData.size());

        return orderStatusData.stream()
                .map(data -> calculatePurchaseOrderStatusWithResult(
                        data.getCurrentStatus(),
                        data.getSupplierStatuses()))
                .toList();
    }

    /**
     * 验证状态计算的合理性
     *
     * @param result 计算结果
     * @return 验证是否通过
     */
    public boolean validateCalculationResult(OrderStatusCalculationResult result) {
        if (result == null) {
            log.warn("计算结果为空");
            return false;
        }

        // 验证状态流转是否合法
        if (result.isStatusChanged()) {
            boolean isValidTransition = statusCalculator.isValidStatusTransition(
                    result.getOriginalStatus(),
                    result.getNewStatus());

            if (!isValidTransition) {
                log.error("非法的状态流转: {} -> {}",
                        result.getOriginalStatus(), result.getNewStatus());
                return false;
            }
        }

        // 验证供应商订单数据的一致性
        if (result.getTotalSupplierOrders() != result.getSupplierStatuses().size()) {
            log.error("供应商订单数量不一致: 统计={}, 实际={}",
                    result.getTotalSupplierOrders(), result.getSupplierStatuses().size());
            return false;
        }

        return true;
    }

    /**
     * 生成状态计算原因说明
     */
    private String generateCalculationReason(TzOrderPurchaseStatusEnum currentStatus,
                                           TzOrderPurchaseStatusEnum newStatus,
                                           List<TzOrderSupplierStatusEnum> supplierStatuses) {

        long completedCount = supplierStatuses.stream()
                .filter(status -> status == TzOrderSupplierStatusEnum.COMPLETED)
                .count();

        long totalCount = supplierStatuses.size();

        return switch (newStatus) {
            case ORDER_CANCELLED -> "所有供应商订单都已取消";
            case PAYMENT_PENDING -> "存在待支付的供应商订单";
            case IN_STOCK -> String.format("所有%d个供应商订单都已完成", totalCount);
            case WAREHOUSE_RECEIVED -> "所有供应商订单的商品都已送达仓库";
            case SUPPLIER_SHIPPED -> "所有供应商订单都已发货";
            case PARTIALLY_FULFILLED -> String.format("部分供应商订单已完成 (%d/%d)", completedCount, totalCount);
            case PROCUREMENT_IN_PROGRESS -> "所有供应商订单都已支付，正在采购中";
            default -> String.format("状态从 %s 变更为 %s", currentStatus.getDescription(), newStatus.getDescription());
        };
    }

    /**
     * 获取状态计算器实例（供其他服务使用）
     */
    public OrderStatusCalculator getStatusCalculator() {
        return statusCalculator;
    }

    /**
     * 采购订单状态数据封装类
     */
    public static class PurchaseOrderStatusData {
        private final Long orderId;
        private final TzOrderPurchaseStatusEnum currentStatus;
        private final List<TzOrderSupplierStatusEnum> supplierStatuses;

        public PurchaseOrderStatusData(Long orderId,
                                     TzOrderPurchaseStatusEnum currentStatus,
                                     List<TzOrderSupplierStatusEnum> supplierStatuses) {
            this.orderId = orderId;
            this.currentStatus = currentStatus;
            this.supplierStatuses = supplierStatuses;
        }

        public Long getOrderId() { return orderId; }
        public TzOrderPurchaseStatusEnum getCurrentStatus() { return currentStatus; }
        public List<TzOrderSupplierStatusEnum> getSupplierStatuses() { return supplierStatuses; }
    }
}
